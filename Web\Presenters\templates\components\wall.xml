<div class="content_divider">
    <div class="content_title_expanded">
        {_wall} 
        <nobold>
            {tr("wall", $count)}
            <a href="/wall{$owner}" class="float-right lowercase">{_all_title}</a>
        </nobold>
    </div>
    <div>
    <div class="insertThere" id="postz"></div>
    <div id="underHeader">
        <div n:if="$canPost" class="content_subtitle">
            {include "../components/textArea.xml", route => "/wall$owner/makePost", graffiti => true, polls => true, notes => true, hasSource => true, geo => true, docs => true}
        </div>
        
        <div class="content scroll_container">
            {if sizeof($posts) > 0}
                <div class='scroll_node' n:foreach='$posts as $post' data-uniqueid="{$post->getPrettyId()}">
                    <a name="postGarter={$post->getId()}"></a>
                    
                    {include "../components/post.xml", post => $post, commentSection => true}
                </div>
                {include "../components/paginator.xml", conf => $paginatorConf}
            {else}
                {_no_posts_abstract}
            {/if}
        </div>
    </div>
    </div>
</div>
