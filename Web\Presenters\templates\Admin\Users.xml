{extends "@layout.xml"}
{var $search = true}

{block title}
    {_admin_user_search}
{/block}

{block heading}
    {_users}
{/block}

{block searchTitle}
    {include title}
{/block}

{block content}
    {var $users  = iterator_to_array($users)}
    {var $amount = sizeof($users)}
    
    <table class="aui aui-table-list">
        <thead>
            <tr>
                <th>ID</th>
                <th>UUID</th>
                <th>{_admin_name}</th>
                <th>{_gender}</th>
                <th>{_admin_shortcode}</th>
                <th>{_registration_date}</th>
                <th>{_admin_actions}</th>
            </tr>
        </thead>
        <tbody>
            <tr n:foreach="$users as $user">
                <td>{$user->getId()}</td>
                <td>{$user->getChandlerGUID()}</td>
                <td>
                    <span class="aui-avatar aui-avatar-xsmall">
                        <span class="aui-avatar-inner">
                            <img src="{$user->getAvatarUrl('miniscule')}" alt="{$user->getCanonicalName()}" style="object-fit: cover;" role="presentation" />
                        </span>
                    </span>

                    <a href="{$user->getURL()}">{$user->getCanonicalName()}</a>

                    <span n:if="$user->isBanned()" class="aui-lozenge aui-lozenge-subtle aui-lozenge-removed">{_admin_banned}</span>
                </td>
                <td>{$user->isFemale() ? tr("female") : tr("male")}</td>
                <td>{$user->getShortCode() ?? "(" . tr("none") . ")"}</td>
                <td>{$user->getRegistrationTime()}</td>
                <td>
                    <a class="aui-button aui-button-primary" href="/admin/users/id{$user->getId()}">
                        <span class="aui-icon aui-icon-small aui-iconfont-new-edit">{_edit}</span>
                    </a>
                    {if $thisUser->getChandlerUser()->can("substitute")->model('openvk\Web\Models\Entities\User')->whichBelongsTo(0)}
                        <a class="aui-button" href="/setSID/{$user->getChandlerUser()->getId()}?hash={rawurlencode($csrfToken)}">
                            <span class="aui-icon aui-icon-small aui-iconfont-sign-in">{_admin_loginas}</span>
                        </a>
                    {/if}
                </td>
            </tr>
        </tbody>
    </table>
    <br/>
    <div align="right">
        {var $isLast = ((20 * (($_GET['p'] ?? 1) - 1)) + $amount) < $count}

        <a n:if="($_GET['p'] ?? 1) > 1" class="aui-button" href="?p={($_GET['p'] ?? 1) - 1}">&laquo;</a>
        <a n:if="$isLast" class="aui-button" href="?p={($_GET['p'] ?? 1) + 1}">&raquo;</a>
    </div>
{/block}
