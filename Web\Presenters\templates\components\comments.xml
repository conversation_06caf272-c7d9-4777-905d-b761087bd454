<div>
    <h4 n:if="$showTitle ?? true">{_comments} ({$count})</h4>

    {if !is_null($sort) && $count > 5}
        <a class="sort_link" n:attr="href => $sort == 'desc' ? '?sort=asc' : '?sort=desc'">
            {if $sort == 'desc'}
                {_new_first}
            {else}
                {_old_first}
            {/if}

            <div n:class="sort_link_icon, $sort == 'desc' ? sort_link_icon_desc : sort_link_icon_asc"></div>
        </a>
    {/if}
</div>
<div n:ifset="$thisUser" id="standaloneCommentBox">
    {var $commentsURL = "/al_comments/create/$model/" . $parent->getId()}
    {var $club = $parent instanceof \openvk\Web\Models\Entities\Post && $parent->getTargetWall() < 0 ? (new openvk\Web\Models\Repositories\Clubs)->get(abs($parent->getTargetWall())) : $club}
    {if !$readOnly}
        {include "textArea.xml", route => $commentsURL, postOpts => false, graffiti => (bool) ovkGetQuirk("comments.allow-graffiti"), club => $club, custom_id => $custom_id}
    {/if}
</div>

{if sizeof($comments) > 0}
    <div class='scroll_container'>
        <div class='scroll_node' n:foreach="$comments as $comment">
            {include "comment.xml", comment => $comment, no_reply_button => $readOnly}
        </div>
    </div>
    <div style="margin-top: 11px;">
        {include "paginator.xml", conf => (object) ["page" => $page, "count" => $count, "amount" => sizeof($comments), "perPage" => 10]}
    </div>
{else}
    {_comments_tip}
{/if}
