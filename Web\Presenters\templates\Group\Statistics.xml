{extends "../@layout.xml"}
{block title}{$club->getName()} » {_statistics}{/block}

{block header}
    <a href="{$club->getURL()}">{$club->getName()}</a> » {_statistics}
{/block}

{block content}
    <div>
        <h4>{_coverage}</h4>
        <p>{_coverage_this_week}</p>
        <div id="reachChart" style="width: 100%; height: 280px;"></div>
        
        <h4>{_views}</h4>
        <p>{_views_this_week}</p>
        <div id="viewsChart" style="width: 100%; height: 280px;"></div>
        
        <style>
            .modebar-container {
                display: none !important;
            }
        </style>
        {script "js/node_modules/plotly.js-dist/plotly.js"}
        <script>
            function makePlot(selector, datum) {
                Plotly.newPlot(document.querySelector(selector), [datum.total, datum.subs, datum.viral], {
                    margin: { t: 0 },
                    layout: { showlegend: true, legend: { xanchor: 'center', x: 0.5, orientation: 'h' } }
                });
            }
            
            let reach = {$reach};
            let views = {$views};
            
            makePlot("#reachChart", reach);
            makePlot("#viewsChart", views);
        </script>
    </div>
{/block}
