{extends "../@layout.xml"}

{block title}
    {_gift_select}
{/block}

{block header}
    <a href="{$user->getURL()}">{$user->getCanonicalName()}</a> »
    <a href="/gifts?act=pick&user={$user->getId()}">{_gift_select}</a> »
    <a href="/gifts?act=pick&user={$user->getId()}">{_collections}</a> »
    {$cat->getName(tr("__lang"))}
{/block}

{block content}
    <div class="gift_grid scroll_container">
        <a href='/gifts?act=confirm&user={$user->getId()}&pack={$cat->getId()}&elid={$gift->getId()}' n:foreach="$gifts as $gift" n:class="scroll_node, gift_sel, !$gift->canUse($thisUser) ? disabled" data-gift="{$gift->getId()}">
            <img class="gift_pic" src="{$gift->getImage(2)}" alt="{_gift}" loading=lazy />

            <strong class="gift_price">
                {if $gift->isFree()}
                    {_free_gift}
                {else}
                    {tr('coins', $gift->getPrice())}
                {/if}
            </strong>

            <strong class="gift_limit">
                {if $gift->getUsagesLeft($thisUser) !== INF}
                    {tr("gifts_left", $gift->getUsagesLeft($thisUser))}
                {/if}&nbsp;
            </strong>
        </a>
    </div>

    <div style="padding: 8px;">
        {include "../components/paginator.xml", conf => (object) [
            "page"    => $page,
            "count"   => $count,
            "amount"  => sizeof($gifts),
            "perPage" => OPENVK_DEFAULT_PER_PAGE,
        ]}
    </div>
{/block}
