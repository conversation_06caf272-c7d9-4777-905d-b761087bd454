{var $author = $post->getOwner()}
{var $platform = $post->getPlatform()}
{var $platformDetails = $post->getPlatformDetails()}
{var $wallOwner = $post->getWallOwner()}
{var $likesCount = $post->getLikesCount()}
{var $repostsCount = $post->getRepostCount()}
{var $commentsCount = $post->getCommentsCount()}
{var $canBePinned = $post->canBePinnedBy($thisUser ?? NULL)}
{var $canBeDeleted = $post->canBeDeletedBy($thisUser)}
{if $post->isDeactivationMessage() && $post->getText()}
    {var $deac = "post_deact"}
{else}
    {var $deac = "post_deact_silent"}
{/if}



<table border="0" style="font-size: 11px;" data-id="{$post->getPrettyId()}" n:class="post, $post->isExplicit() ? post-nsfw">
    <tbody>
        <tr>
            <td width="54" valign="top">
                <a href="{$author->getURL()}">
                    <img src="{$author->getAvatarURL('miniscule')}" class="post-avatar" width="50" loading=lazy />
                    <span n:if="!$post->isPostedOnBehalfOfGroup() && !($compact ?? false) && $author->isOnline()" class="post-online">{_online}</span>
                </a>
            </td>
            <td width="100%" valign="top">
                <div class="post-author">
                    <a href="{$author->getURL()}"><b class="post-author-name">{$author->getCanonicalName()}</b></a>
                    <img n:if="$author->isVerified()" class="name-checkmark" src="/assets/packages/static/openvk/img/checkmark.png">
                    {if $post->isDeactivationMessage()}
                        {$author->isFemale() ? tr($deac . "_f") : tr($deac . "_m")}
                    {elseif $post->isUpdateAvatarMessage() && !$post->isPostedOnBehalfOfGroup()}
                        {$author->isFemale() ? tr("upd_f") : tr("upd_m")}
                    {elseif $post->isUpdateAvatarMessage() && $post->isPostedOnBehalfOfGroup()}
                        {tr("upd_g")}
                    {elseif $post->isPostedOnBehalfOfGroup()}
                        {_post_writes_g}
                    {else}
                        {if $author->isFemale()}
                            {_post_writes_f}
                        {elseif $author->isNeutral()}
                            {_post_writes_g}
                        {else}
                            {_post_writes_m}
                        {/if}
                    {/if}
                    {if ($onWallOf ?? false) &&!$post->isPostedOnBehalfOfGroup() && $post->getOwnerPost() !== $post->getTargetWall()}
                        <a href="{$wallOwner->getURL()}" class="mention" data-mention-ref="{$post->getTargetWall()}">
                            <b>
                                {if isset($thisUser) && $thisUser->getId() === $post->getTargetWall()}
                                    {_post_on_your_wall}
                                {elseif $wallOwner instanceof \openvk\Web\Models\Entities\Club}
                                    {tr("post_on_group_wall", ovk_proc_strtr($wallOwner->getName(), 52))}
                                {else}
                                    {tr("post_on_user_wall", $wallOwner->getMorphedName("genitive", false))}
                                {/if}
                            </b>
                        </a>
                    {/if}
                    <br/>
                    <a href="{if !$suggestion}/wall{$post->getPrettyId()}{else}javascript:void(0){/if}" class="date">
                        {$post->getPublicationTime()} <span n:if="$post->getEditTime()" class="editedMark">({_edited_short})</span>{if $post->isPinned()}, {_pinned}{/if}
                        
                        <a n:if="!empty($platform)" class="client_app" data-app-tag="{$platform}" data-app-name="{$platformDetails['name']}" data-app-url="{$platformDetails['url']}" data-app-img="{$platformDetails['img']}">
                            <img src="/assets/packages/static/openvk/img/app_icons_mini/{$post->getPlatform(this)}.svg">
                        </a>
                    </a>
                </div>
                <div class="post-content" id="{$post->getPrettyId()}" data-localized-nsfw-text="{_nsfw_warning}">
                    <div class="text" id="text{$post->getPrettyId()}">
                        {var $owner = $author->getId()}

                        <span data-text="{$post->getText(false)}" class="really_text">{$post->getText()|noescape}</span>

                        {var $width = ($GLOBALS["_bigWall"] ?? false) ? 550 : 320}
                        {if isset($GLOBALS["_nesAttGloCou"])}
                            {var $width = $width - 70 * $GLOBALS["_nesAttGloCou"]}
                        {/if}
                        {var $attachmentsLayout = $post->getChildrenWithLayout($width)}
                        <div n:ifcontent class="attachments attachments_b" style="height: {$attachmentsLayout->height|noescape}; width: {$attachmentsLayout->width|noescape};">
                            <div class="attachment" n:foreach="$attachmentsLayout->tiles as $attachment" style="float: {$attachment[3]|noescape}; width: {$attachment[0]|noescape}; height: {$attachment[1]|noescape};">
                                {include "../attachment.xml", attachment => $attachment[2], parent => $post, parentType => "post", tilesCount => sizeof($attachmentsLayout->tiles)}
                            </div>
                        </div>

                        <div n:ifcontent class="attachments attachments_m">
                            <div class="attachment" n:foreach="$attachmentsLayout->extras as $attachment">
                                {include "../attachment.xml", attachment => $attachment}
                            </div>
                        </div>
                    </div>
                    <div n:if="$post->getGeo()" class="post-geo">
                        <div onclick="javascript:openGeo({$post->getGeo()}, {$post->getTargetWall()}, {$post->getVirtualId()})">
                            <svg class="map_svg_icon" width="13" height="12" viewBox="0 0 3.4395833 3.175">
                                <g><path d="M 1.7197917 0.0025838216 C 1.1850116 0.0049444593 0.72280427 0.4971031 0.71520182 1.0190592 C 0.70756921 1.5430869 1.7223755 3.1739665 1.7223755 3.1739665 C 1.7223755 3.1739665 2.7249195 1.5439189 2.7243815 0.99632161 C 2.7238745 0.48024825 2.2492929 0.00024648357 1.7197917 0.0025838216 z M 1.7197917 0.52606608 A 0.48526123 0.48526123 0 0 1 2.2050334 1.0113078 A 0.48526123 0.48526123 0 0 1 1.7197917 1.4965495 A 0.48526123 0.48526123 0 0 1 1.23455 1.0113078 A 0.48526123 0.48526123 0 0 1 1.7197917 0.52606608 z " /></g>
                            </svg>
                            <a>{$post->getGeo()->name ?? tr("admin_open")}</a>
                        </div>
                    </div>
                    <div n:if="$suggestion && $canBePinned" class="suggestionControls" style="margin-bottom: 7px;">
                        <input type="button" class="button" id="publish_post" data-id="{$post->getId()}" value="{_publish_suggested}">
                        <input type="button" class="button" id="decline_post" data-id="{$post->getId()}" value="{_decline_suggested}">
                    </div>
                    <div n:if="$post->isAd()" style="color:grey;">
                        <br/>
                        &nbsp;! {_post_is_ad}
                    </div>
                    <div n:if="$post->hasSource()" class="sourceDiv">
                        <span>{_source}: {$post->getSource(true)|noescape}</span>
                    </div>
                    <div n:if="$post->isSigned()" class="post-signature">
                        {var $actualAuthor = $post->getOwner(false)}
                        <span>
                            {_author}:
                            <a href="{$actualAuthor->getURL()}" class="mention" data-mention-ref="{$actualAuthor->getId()}">
                               {$actualAuthor->getCanonicalName()}
                            </a>
                        </span>
                    </div>
                </div>
                <div class='post-edit' n:if='!$compact'></div>
                <div n:if="!($compact ?? false)" class="post-menu">
                    {if is_null($thisUser)}
                        {var $forceNoDeleteLink = true}
                        {var $forceNoPinLink = true}
                    {/if}

                    {if !($forceNoEditLink ?? false) && $post->canBeEditedBy($thisUser)}
                        <a id="editPost" 
                           data-id="{$post->getPrettyId()}">{_edit}</a> &nbsp;|&nbsp;
                    {/if}

                    {if !($forceNoDeleteLink ?? false) && $canBeDeleted}
                        <a href="/wall{$post->getPrettyId()}/delete">{_delete}</a> &nbsp;|&nbsp;
                    {/if} 

                    {if $feedIgnoreButton && !$canBeDeleted}
                    <a id="__ignoreSomeoneFeed" data-val='1' data-id="{$wallOwner->getRealId()}">{_feed_ignore}</a> &nbsp;|&nbsp;
                    {/if}

                    {if !$canBeDeleted}
                        <a href="javascript:reportPost({$post->getId()})">{_report}</a> &nbsp;|&nbsp;
                    {/if}

                    {if !($forceNoPinLink ?? false) && $canBePinned}
                        {if $post->isPinned()}
                            <a href="/wall{$post->getPrettyId()}/pin?act=unpin&hash={rawurlencode($csrfToken)}">{_unpin}</a>
                        {else}
                            <a href="/wall{$post->getPrettyId()}/pin?act=pin&hash={rawurlencode($csrfToken)}">{_pin}</a>
                        {/if}
                        &nbsp;|&nbsp;
                    {/if}
                    
                    <a n:if="!($forceNoCommentsLink ?? false)" href="/wall{$post->getPrettyId()}#comments">
                        {_comments}
                        {if $commentsCount > 0}
                            (<b>{$commentsCount}</b>)
                        {/if}
                    </a>

                    {if !($forceNoCommentsLink ?? false) && !($forceNoShareLink ?? false)}
                        &nbsp;|&nbsp;
                    {/if}
                    
                    <a n:if="!($forceNoShareLink ?? false)" id="reposts{$post->getPrettyId()}" class="post-share-button" {ifset $thisUser} href="javascript:repost('{$post->getPrettyId()}', 'post')"{/ifset}>
                        {_share}
                        {if $repostsCount > 0}
                            (<b id="repostsCount{$post->getPrettyId()}">{$repostsCount}</b>)
                        {/if}
                    </a>

                    <div n:if="!($forceNoLike ?? false)" class="like_wrap">
                        {ifset $thisUser}
                            {var $liked = $post->hasLikeFrom($thisUser)}
                            <a href="/wall{$post->getPrettyId()}/like?hash={rawurlencode($csrfToken)}" class="post-like-button" data-liked="{(int) $liked}" data-likes="{$likesCount}" data-id="{$post->getPrettyId()}" data-type='post'>
                                <div class="heart" id="{if $liked}liked{/if}"></div>
                                <span class="likeCnt">{if $likesCount > 0}{$likesCount}{/if}</span>
                            </a>
                        {else}
                            <a n:if="$likesCount > 0" class="post-like-button">
                                <div class="heart"></div>
                                <span class="likeCnt">{$likesCount}</span>
                            </a>
                        {/ifset}
                    </div>
                </div>
            </td>
        </tr>
    </tbody>
</table>