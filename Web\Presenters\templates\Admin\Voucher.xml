{extends "@layout.xml"}

{block title}
    {_edit}
{/block}

{block heading}
    {_edit} #{$form->token ?? "undefined"}
{/block}

{block content}
    <div style="margin: 8px -8px;" class="aui-tabs horizontal-tabs">
        <ul class="tabs-menu">
            <li class="menu-item active-tab">
                <a href="#info">{_admin_tab_main}</a>
            </li>
            <li class="menu-item">
                <a href="#activators">{_voucher_activators}</a>
            </li>
        </ul>
        <div class="tabs-pane active-pane" id="info">
            <form class="aui" method="POST">
                <div class="field-group">
                    <label for="id">ID</label>
                    <input class="text long-field" type="number" id="id" name="id" disabled value="{$form->id}" />
                </div>
                <div class="field-group">
                    <label for="token">{_admin_voucher_serial}</label>
                    <input class="text long-field" type="text" id="token" name="token" value="{$form->token}" />
                    <div class="description">{_admin_voucher_serial_desc}</div>
                </div>
                <div class="field-group">
                    <label for="coins">{_admin_voucher_coins}</label>
                    <input class="text long-field" type="number" min="0" id="coins" name="coins" value="{$form->coins}" />
                </div>
                <div class="field-group">
                    <label for="rating">{_admin_voucher_rating_number}</label>
                    <input class="text long-field" type="number" min="0" id="rating" name="rating" value="{$form->rating}" />
                </div>
                <div class="field-group">
                    <label for="usages">
                        {if $form->id === 0}
                            {_usages_total}
                        {else}
                            {_usages_left}
                        {/if}
                    </label>
                    <input class="text long-field" type="number" min="-1" id="usages" name="usages" value="{$form->usages}" />
                    <div class="description">{_admin_voucher_usages_desc}</div>
                </div>

                <div class="buttons-container">
                    <div class="buttons">
                        <input type="hidden" name="hash" value="{$csrfToken}" />
                        <input class="aui-button aui-button-primary submit" type="submit" value="{_save}">
                    </div>
                </div>
            </form>
        </div>
        <div class="tabs-pane" id="activators">
            <table rules="none" class="aui aui-table-list">
                <tbody>
                    <tr n:foreach="$form->users as $user">
                        <td>
                            <span class="aui-avatar aui-avatar-xsmall">
                                <span class="aui-avatar-inner">
                                    <img src="{$user->getAvatarUrl('miniscule')}" alt="{$user->getCanonicalName()}" role="presentation" />
                                </span>
                            </span>

                            <a href="{$user->getURL()}">{$user->getCanonicalName()}</a>

                            <span n:if="$user->isBanned()" class="aui-lozenge aui-lozenge-subtle aui-lozenge-removed">{_admin_banned}</span>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
{/block}
