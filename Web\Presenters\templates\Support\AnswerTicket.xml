{extends "../@layout.xml"}
{block title}{_menu_help}{/block}

{block header}
    {$ticket->getName()}
{/block}

{block content}
    <div class="post-author">
        <a href="#" style="font-size: 13px;"><b>{$ticket->getName()}</b></a><br />
        {_author}:
            <a href="/id{$ticket->getUser()->getId()}">
                {$ticket->getUser()->getFullName()}</a>
                | {$ticket->getUser()->getRegistrationIP()}
                | {_status}: {$ticket->getStatus()}.
                | <b n:if="$ticket->getUser()->isBanned()" style="color: red; cursor: pointer;" onclick="$('#ban-reason').toggle();">Блокировка</b>
            <div id="ban-reason" style="display: none; padding: 8px;">
                <h4 style="padding: 8px;">Причина блокировки</h4>
                <div style="padding: 8px;">Так пользователь видит экран с информацией о блокировке:</div>
                <div style="padding: 16px; border: 1px solid #C4C4C4; margin: 8px;">
                    {var $ban = $ticket->getUser()->getBanReason("banned")}
                    <center>
                        <img src="/assets/packages/static/openvk/img/oof.apng" alt="{_banned_alt}" style="width: 20%;" />
                    </center>
                    <p>
                        {if is_string($ban)}
                            {tr("banned_1", htmlentities($ticket->getUser()->getCanonicalName()))|noescape}<br/>
                            {tr("banned_2", htmlentities($ban))|noescape}
                        {else}
                            {tr("banned_1", htmlentities($ticket->getUser()->getCanonicalName()))|noescape}
                            <div>
                                Эта страница была заморожена {$ban[0]|noescape}
                                {if $ban[1] !== "app"}
                                    {include "../Report/ViewContent.xml", type => $ban[1], object => $ban[2]}
                                {/if}
                            </div>
                        {/if}

                        {if !$ticket->getUser()->getUnbanTime()}
                            {_banned_perm}
                        {else}
                            {tr("banned_until_time", $ticket->getUser()->getUnbanTime())|noescape}
                        {/if}
                    </p>
                </div>
            </div>
    </div>
    <div class="text" style="padding-top: 10px; border-bottom: #ECECEC solid 1px;">
        {$ticket->getText()|noescape}
        <br /><br />
    </div>
    <div style="padding-top: 5px;">
        {$ticket->getTime()}&nbsp;|&nbsp;
        <a href="/support/delete/{$id}?hash={$csrfToken}">{_delete}</a>
    </div>
    <br />
    <div>
        <form action="/al_comments/create/support/reply/{$id}" method="post" style="margin: 0;">
            <textarea name="text" id="answer_text" style="width: 100%; resize: vertical;"></textarea>
            <br />
            <input type="hidden" name="hash" value="{$csrfToken}" />
            <br />
            <div style="float: left;">
                <input type="submit" value="{_write}" class="button" />
                <select name="status" style="width: unset;">
                    <option value="1">{_support_status_1}</option>
                    <option value="2">{_support_status_2}</option>
                    <option value="0">{_support_status_0}</option>
                </select>
            </div>
            <div n:if="!is_null($fastAnswers)" style="float: right;">
                <a class="button" href="javascript:showSupportFastAnswerDialog(fastAnswers)">{_fast_answers}</a>
            </div>
            <br />
        </form>
    </div>
    <br />
    <p n:if="!$comments">{_no_comments}</p>
    {var $printedSupportGreeting = false}
    <table n:foreach="$comments as $comment" border="0" style="font-size: 11px;" class="post">
        <tbody>
            <tr>
                {if $comment->getUType() === 0}
                    <td width="54" valign="top">
                        <img src="{$comment->getUser()->getAvatarUrl('miniscule')}" width="50" />
                    </td>
                {else}
                    <td width="54" valign="top">
                        <img
                            src="{$comment->getAvatar()}"
                            style="max-width: 50px; filter: hue-rotate({$comment->getColorRotation()}deg);" />
                    </td>
                {/if}
                <td width="645" valign="top">
                    {if $comment->getUType() === 0}
                        <div class="post-author">
                        <a href="{$comment->getUser()->getURL()}"><b>
                        {$comment->getUser()->getFullName()}
                        </b></a> {($comment->getUser()->isFemale() ? tr("post_writes_f") : tr("post_writes_m"))}<br />
                        <a href="#" class="date">{$comment->getTime()}</a>
                        </div>
                    {elseif ($comment->getUType() === 1)}
                        <div class="post-author">
                            <a n:attr="href => $thisUser->getChandlerUser()->can('write')->model('openvk\Web\Models\Entities\TicketReply')->whichBelongsTo(0) ? '/support/agent' . $comment->getUser()->getId() : ''"><b>{$comment->getAuthorName()}</b></a>
                            {if $thisUser->getChandlerUser()->can("write")->model('openvk\Web\Models\Entities\TicketReply')->whichBelongsTo(0)}
                                <a href="{$comment->getUser()->getURL()}">
                                    <span class="nobold">
                                        {var $lastName = $comment->getUser()->getLastName()}
                                        {if empty(trim($lastName))}
                                            ({$comment->getUser()->getFirstName()})
                                        {else}
                                            ({$comment->getUser()->getFirstName()} {iconv_substr($lastName, 0, 1)}.)
                                        {/if}
                                    </span>
                                </a>
                            {/if}
                            {_post_writes_m}<br />
                            <a href="#" class="date">{$comment->getTime()}</a>
                        </div>
                    {/if}
                    <div class="post-content" id="{$comment->getId()}">
                        <div class="text" id="text{$comment->getId()}">
                            {if $comment->getUType() === 1 && !$printedSupportGreeting}
                                {var $printedSupportGreeting = true}
                                {tr("support_greeting_hi", $ticket->getUser()->getFullName())}
                                <br />
                                <br />

                                {$comment->getText()|noescape}
                                <br />
                                <br />

                                {tr("support_greeting_regards", OPENVK_ROOT_CONF["openvk"]["appearance"]["name"])|noescape}
                            {else}
                                {$comment->getText()|noescape}
                            {/if}
                        </div>

                        <div class="post-menu">
                            <a href="/support/comment/{$comment->getId()}/delete?hash={urlencode($csrfToken)}">{_delete}</a>
                        </div>
                        
                        {if $comment->getUType() === 1 && !is_null($comment->isLikedByUser())}
                            <div class="post-menu">
                                <strong>
                                    {if $comment->isLikedByUser()}
                                        {_support_good_answer_agent}
                                    {else}
                                        {_support_bad_answer_agent}
                                    {/if}
                                </strong>
                            </div>
                        {/if}
                    </div>
                </td>
            </tr>
        </tbody>
    </table>

    <script>
        const fastAnswers = [
            {foreach $fastAnswers as $answer}
                {$answer},
            {/foreach}
        ];
    </script>
{/block}
