{extends "../@layout.xml"}

{block title}{_edit_playlist}{/block}

{block header}
    <a href="{$owner->getURL()}">{$owner->getCanonicalName()}</a>
    »
    <a href="/playlists{$ownerId}">{_playlists}</a>
    »
    <a href="/playlist{$playlist->getPrettyId()}">{_playlist}</a>
    »
    {_edit_playlist}
{/block}

{block content}
    <div class='PE_wrapper'>
        <div class='PE_playlistEditPage'>
            <div class="playlistCover">
                <a onclick="document.querySelector(`input[name='cover']`).click()">
                    <input type='file' name='cover' style='display:none;' accept=".jpg,.png">
                    <img src="{$playlist->getCoverURL('normal')}" alt="{_playlist_cover}">
                </a>
                <div class="profile_links" style="width: 139px;">
                    <a class="profile_link" style="width: 98%;" id="_deletePlaylist" data-id="{$playlist->getId()}">{_delete_playlist}</a>
                </div>
            </div>

            <div class="PE_playlistInfo">
                <div>
                    <input value='{$playlist->getName()}' type="text" name="title" placeholder="{_title}" maxlength="125" />
                </div>
                <div class="moreInfo">
                    <textarea placeholder="{_description}" name="description" maxlength="2045">{$playlist->getDescription()}</textarea>
                </div>
                <label>
                    <input type='checkbox' name='is_unlisted' value='1' n:attr='checked => $playlist->isUnlisted()'>
                    {_playlist_hide_from_search}
                </label>
                <a id='_playlistAppendTracks'>{_add_audio_verb}</a>
            </div>
        </div>
        <div class='PE_audios generic_audio_list'>
            <div n:foreach='$audios as $audio' class='vertical-attachment upload-item' data-id='{$audio->getId()}'>
                <div class='vertical-attachment-content'>
                    {include 'player.xml', audio => $audio, hideButtons => true}
                </div>
                <div class="vertical-attachment-remove">
                    <div id="small_remove_button"></div>
                </div>
            </div>
        </div>
        <div class='PE_end'>
            <input class="button" type="button" id='playlist_edit' value="{_save}">
        </div>
    </div>
{/block}
