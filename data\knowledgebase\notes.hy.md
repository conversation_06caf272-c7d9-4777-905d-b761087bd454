OpenVK-KB-Heading: Խմբագրել նշումները

OpenVK–ի վիքի–նիշքը դա XHTML1.0 Transitional շարժիչն է։ Միակ տարբերությունը այն է որ այստեղ մենք հեռացրել ենք տեգերը, որոնք պետք չեն կամ կարող են վնասել OpenVK-ն և իր օգտատերերին։

Թույլատրված տեգեր․
* Բոլոր երրորդից վեցերորդ մակարդակի գլխային անուններ (h3-h6)
* Պարբերություններ (&lt;p&gt;)
* Տեքստի ֆորմատավորում (&lt;i&gt;, &lt;b&gt;, &lt;del&gt;)
* &lt;sup&gt;, &lt;sub&gt;, &lt;ins&gt;
* Ամեն ինչ վերաբերվող աղյուսակներին
* Հղումներ և նկարներ (&lt;a&gt;, &lt;img&gt;)
* Ցուցակներ (and &lt;ol&gt; and &lt;ul&gt;)
* Գծային ժապավեն և հորիզոնական առանձնացուցիչ (hr)
* Բլոկ–ցիտատներ (&lt;blockquote&gt; and &lt;cite&gt;)
* &lt;հապավում&gt;

**Նշում**: նկարները չե կարող ունենալ աղբյուրների մափ և իրենց աղբյուրը պետք է OpenVK–ի հոսքում պահպանված ֆայլ լինի։ Այսպիսի սահմանափակում չի տարածվում հղումների վրա։ Որպես հղում կարող է ներկայացվել ամեն ինչ (ի բացառություն data: և javascript: պսեուդոպրոտոկոլներին)։ Այնուամենայնիվ, դրանք հետաձգվելու են:

Դուք կարող եք նկատել, որ &lt;style&gt;–ը թույլատրված ցուցակում է, չնայած մենք չենք սպասարկում &lt;div&gt; և &lt;img&gt; տեգերը հենց այդ ատտրիբուտով։ Թույլատրվում են հետևյալ CSS տեգերը․
* float
* height
* width
* max-height
* max-width
* font-weight

Եթե կարգավորումը չափի հատկություն է, այն կարող է ընդունել միայն պիքսելները որպես արժեք (ոչ %, pt, pc, em, rem, vw or vh).
