{extends "../@layout.xml"}
{block title}{_edit_topic} "{$topic->getTitle()}"{/block}

{block header}
    <a href="{$club->getURL()}">{$club->getCanonicalName()}</a>
    » 
    <a href="/board{$club->getId()}">{_discussions}</a>
    » 
    {_edit_topic}
{/block}

{block content}
    <div class="container_gray">
        <b>{$topic->getTitle()}</b>
        <br />
        <a href="{$topic->getOwner()->getURL()}">{$topic->getOwner()->getCanonicalName()}</a>
    </div>

    <form method="POST" enctype="multipart/form-data" style="margin-top: 20px;">
        <table cellspacing="7" cellpadding="0" width="80%" border="0" align="center">
            <tbody>
                <tr>
                    <td width="120" valign="top">
                        <span class="nobold">{_title}</span>
                    </td>
                    <td>
                        <input type="text" name="title" style="width: 100%;" value="{$topic->getTitle()}" />
                    </td>
                </tr>
                <tr>
                    <td width="120" valign="top">
                        <span class="nobold">{_topic_settings}</span>
                    </td>
                    <td>
                        {if $topic->getClub()->canBeModifiedBy($thisUser)}
                            <label><input type="checkbox" name="pin" n:attr="checked => $topic->isPinned()" /> {_pin_topic}</label><br />
                        {/if}
                        <label><input type="checkbox" name="close" n:attr="checked => $topic->isClosed()" /> {_close_topic}</label>
                    </td>
                </tr>
                <tr>
                    <td>
                        <a id="_anotherDelete" class="button" href="/topic{$topic->getPrettyId()}/delete?hash={urlencode($csrfToken)}">{_delete_topic}</a>
                    </td>
                    <td>
                        <input type="hidden" name="hash" value="{$csrfToken}" />
                        <input type="submit" value="{_save}" class="button" />
                    </td>
                </tr>
            </tbody>
        </table>

        <input type="hidden" name="hash" value="{$csrfToken}" />
    </form>
{/block}
