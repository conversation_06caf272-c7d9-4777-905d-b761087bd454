{extends "../@layout.xml"}
{block title}{_my_messages}{/block}

{block header}{_my_messages}{/block}

{block content}
    <div class="tabs">
        <div id="activetabs" class="tab">
            <a id="act_tab_a" href="javascript:false">{_all_messages}</a>
        </div>
    </div>

    <div class="container_gray">
        <form action="/im/search" method="POST" style="margin: 0;">
            <input type="text" name="pattern" placeholder="{_search_messages}" required />
        </form>
    </div>

    {if sizeof($corresps) > 0}
        <div class="crp-list scroll_container">
            <div n:foreach="$corresps as $coresp"
                 class="scroll_node crp-entry"
                 onmousedown="window.location.assign({$coresp->getURL()});" >
                {var $recipient = $coresp->getCorrespondents()[1]}
                {var $lastMsg   = $coresp->getPreviewMessage()}

                <div class="crp-entry--image">
                    <img src="{$recipient->getAvatarURL('miniscule')}"
                    alt="Фотография пользователя" loading=lazy />
                </div>
                <div class="crp-entry--info">
                    <a href="{$recipient->getURL()}">{$recipient->getCanonicalName()}</a><br/>
                    <span>{$lastMsg->getSendTimeHumanized()}</span>
                </div>
                <div n:class="crp-entry--message, $lastMsg->getUnreadState() ? unread">
                    {var $_author = $lastMsg->getSender()}

                    <div class="crp-entry--message---av" n:if="$_author->getId() === $thisUser->getId()">
                        <img src="{$_author->getAvatarURL('miniscule')}"
                        alt="Фотография пользователя" />
                    </div>
                    <div class="crp-entry--message---text">
                        {$lastMsg->getText()|noescape}
                    </div>
                </div>
            </div>
        </div>
        <div style="margin-top: 3px;">
            {include "../components/paginator.xml", conf => $paginatorConf}
        </div>
    {else}
        <br/>
        <br/>
        <center>{_no_messages}</center>
    {/if}
{/block}