{extends "../@layout.xml"}
{block title}{_view_topic} "{$topic->getTitle()}"{/block}

{block header}
    <a href="{$club->getURL()}">{$club->getCanonicalName()}</a>
    » 
    <a href="/board{$club->getId()}">{_discussions}</a>
    » 
    {_view_topic}

    <div style="float: right;" n:if="$topic->canBeModifiedBy($thisUser)">
        <a href="/topic{$club->getId()}_{$topic->getVirtualId()}/edit">{_edit_topic_action}</a>
    </div>
{/block}

{block content}
    <div class="container_gray">
        <b>{$topic->getTitle()}</b>
        <br />
        <a href="{$topic->getOwner()->getURL()}">{$topic->getOwner()->getCanonicalName()}</a>
        <div class="nobold" style="float: right;">
            {_created} {$topic->getPublicationTime()}
        </div>
    </div>
    <div style="margin-top: 20px;">
        <h4>{tr("topic_messages_count", $count)}</h4>
        {include "../components/comments.xml", comments => $comments, count => $count, page => $page, model => "topics", club => $club, readOnly => $topic->isClosed(), showTitle => false, parent => $topic}
    </div>
{/block}
