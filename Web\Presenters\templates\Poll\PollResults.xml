{if !isset($parentModule) || substr($parentModule, 0, 21) === 'libchandler:absolute.'}
    <link rel="shortcut icon" href="/assets/packages/static/openvk/img/icon.ico" />
    <meta n:ifset="$csrfToken" name="csrf" value="{$csrfToken}" />
    <script src="/language/{getLanguage()}.js" crossorigin="anonymous"></script>
    {script "js/node_modules/jquery/dist/jquery.min.js"}
    {script "js/node_modules/umbrellajs/umbrella.min.js"}
    {script "js/node_modules/msgpack-lite/dist/msgpack.min.js"}
    {script "js/messagebox.js"}
    {script "js/l10n.js"}
    {script "js/al_api.js"}
    {script "js/al_polls.js"}
    {include "../_includeCSS.xml"}

    <style>body { margin: 8px; } .poll { border: 1px solid #e3e3e3; }</style>
{/if}

<div class="poll" data-id="{$id}">
    <a n:if="$unlocked && $voted" href="javascript:pollRetractVote({$id})" class="poll-retract-vote">{_retract_vote}</a>
    <h4>{$title}</h4>
    <div class="poll-results">
        <div n:foreach="$results->options as $option" class="poll-result">
            {if $isAnon}
                <a href="javascript:false">
                    {if $option->voted}
                        <b>{$option->name}</b>
                    {else}
                        {$option->name}
                    {/if}
                </a>
            {else}
                <a href="/poll{$id}/voters?option={base_convert($option->id, 10, 32)}">
                    {if $option->voted}
                        <b>{$option->name}</b>
                    {else}
                        {$option->name}
                    {/if}
                </a>
            {/if}

            <div class="poll-result-barspace">
                <div class="poll-result-bar">
                    <span class="poll-result-count">{$option->votes}</span>
                    <div class="poll-result-bar-sub" style="width: {$option->pct}%">&nbsp;</div>
                </div>
                <div class="poll-result-pct">
                    <strong>{$option->pct}%</strong>
                </div>
            </div>
        </div>
    </div>

    <div class="poll-meta">
        {tr("poll_voter_count", $votes)|noescape}<br/>
        <span class="nobold">{$meta}</span>
    </div>
</div>
