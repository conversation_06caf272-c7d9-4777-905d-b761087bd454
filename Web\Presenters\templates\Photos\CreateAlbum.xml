{extends "../@layout.xml"}
{block title}{_creating_album}{/block}

{block header}
    {ifset $club}
        <a href="{$club->getURL()}">{$club->getName()}</a>
        » 
        <a href="/albums{$club->getId() * -1}">{_albums}</a>
    {else}
        <a href="{$thisUser->getURL()}">{$thisUser->getCanonicalName()}</a>
        » 
        <a href="/albums{$thisUser->getId()}">{_albums}</a>
    {/ifset}
    » 
    {_creating_album}
{/block}

{block content}
<div class="container_gray">
    <h4>{_creating_album}</h4>
    <form method="post" enctype="multipart/form-data">
      <table cellspacing="7" cellpadding="0" width="60%" border="0" align="center">
        <tbody>
          <tr>
            <td width="120" valign="top"><span class="nobold">{_name}:</span></td>
            <td><input type="text" name="name" /></td>
          </tr>
          <tr>
            <td width="120" valign="top"><span class="nobold">{_description}:</span></td>
            <td><textarea name="desc"></textarea></td>
          </tr>
          <tr>
            <td width="120" valign="top"></td>
            <td>
                <input type="hidden" name="hash" value="{$csrfToken}" />
                <input type="submit" class="button" name="submit" value="{_create}" />
            </td>
          </tr>
        </tbody>
      </table>
    </form>
</div>
{/block}
