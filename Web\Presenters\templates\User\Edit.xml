{extends "../@layout.xml"}
{if $mode === 'backdrop'}
    {var $backdrops = $user->getBackDropPictureURLs()}
{/if}

{block title}{_edit_page}{/block}

{block header}
    {_edit_page}
{/block}

{block content}
    {var $isMain      = $mode === 'main'}
    {var $isContacts  = $mode === 'contacts'}
    {var $isInterests = $mode === 'interests'}
    {var $isAdditional = $mode === 'additional'}
    {var $isAvatar    = $mode === 'avatar'}
    {var $isBackDrop  = $mode === 'backdrop'}

    <div n:if="$user->hasPendingNumberChange()" class="msg">
        <b>Подтверждение номера телефона</b><br/>
        Введите код для подтверждения смены номера: <a href="/edit/verify_phone">ввести код</a>.
    </div>

    <div class="tabs">
        <div n:attr="id => ($isMain ? 'activetabs' : 'ki')" class="tab">
            <a n:attr="id => ($isMain ? 'act_tab_a' : 'ki')" href="/edit">{_main}</a>
        </div>
        <div n:attr="id => ($isContacts ? 'activetabs' : 'ki')" class="tab">
            <a n:attr="id => ($isContacts ? 'act_tab_a' : 'ki')" href="/edit?act=contacts">{_contacts}</a>
        </div>
        <div n:attr="id => ($isInterests ? 'activetabs' : 'ki')" class="tab">
            <a n:attr="id => ($isInterests ? 'act_tab_a' : 'ki')" href="/edit?act=interests">{_interests}</a>
        </div>
        <div n:attr="id => ($isAdditional ? 'activetabs' : 'ki')" class="tab">
            <a n:attr="id => ($isAdditional ? 'act_tab_a' : 'ki')" href="/edit?act=additional">{_additional}</a>
        </div>
        <div n:attr="id => ($isAvatar ? 'activetabs' : 'ki')" class="tab">
            <a n:attr="id => ($isAvatar ? 'act_tab_a' : 'ki')" href="/edit?act=avatar">{_avatar}</a>
        </div>
        <div n:attr="id => ($isBackDrop ? 'activetabs' : 'ki')" class="tab">
            <a n:attr="id => ($isBackDrop ? 'act_tab_a' : 'ki')" href="/edit?act=backdrop">{_backdrop_short}</a>
        </div>
    </div>

    <div class="container_gray">
        {if $isMain}

        <h4>{_main_information}</h4>
        <form action="/edit?act=main" method="POST" enctype="multipart/form-data">
            <table cellspacing="7" cellpadding="0" width="60%" border="0" align="center">
                <tbody>
                    <tr>
                        <td width="120" valign="top">
                            <span class="nobold">{_name}: </span>
                        </td>
                        <td>
                            <input type="text" name="first_name" value="{$user->getFirstName(true)}" />
                        </td>
                    </tr>
                    <tr>
                        <td width="120" valign="top">
                            <span class="nobold">{_surname}: </span>
                        </td>
                        <td>
                            <input type="text" name="last_name" value="{$user->getLastName(true)}" />
                        </td>
                    </tr>
                    <tr>
                        <td width="120" valign="top">
                            <span class="nobold">{_nickname}: </span>
                        </td>
                        <td>
                            <input type="text" name="pseudo" value="{$user->getPseudo()}" />
                        </td>
                    </tr>
                    {if OPENVK_ROOT_CONF['openvk']['credentials']['smsc']['enable']}
                    <tr>
                        <td width="120" valign="top">
                            <span class="nobold">{_phone}: </span>
                        </td>
                        <td>
                            <input type="phone" name="phone" value="{$user->getPhone()}" />
                        </td>
                    </tr>
                    {/if}
                    <tr>
                        <td width="120" valign="top">
                            <span class="nobold">{_status}: </span>
                        </td>
                        <td>
                            <input type="text" name="status" value="{$user->getStatus()}" />
                        </td>
                    </tr>
                    <tr>
                        <td width="120" valign="top">
                            <span class="nobold">{_hometown}: </span>
                        </td>
                        <td>
                            <input type="text" name="hometown" value="{$user->getHometown()}" />
                        </td>
                    </tr>
                    <tr>
                        <td width="120" valign="top">
                            <span class="nobold">{_relationship}: </span>
                        </td>
                        <td>
                            <select name="marialstatus" onChange="toggleMaritalStatus(this)">
                                <option n:foreach="range(0, 8) as $i" n:attr="selected => ($user->getMaritalStatus() == $i)" value="{$i}">
                                    {if $user->isFemale()}
                                        {var $str = "relationship_$i"}
                                        {if tr($str . "_fem") == ("@$str" . "_fem")}
                                            {_$str}
                                        {else}
                                            {tr($str . "_fem")}
                                        {/if}
                                    {else}
                                        {_"relationship_$i"}
                                    {/if}
                                </option>
                            </select>
                        </td>
                    </tr>
                    <tr id="maritalstatus-user"
                        n:attr="style => $user->getMaritalStatusUser() || ($user->getMaritalStatus() && !in_array($user->getMaritalStatus(), [0, 1, 8])) ? '' : 'display: none;'">
                        <td width="120" valign="top"></td>
                        <td>
                            <input type="text" placeholder="{_page_address}" name="maritalstatus-user"
                                   n:attr="value => $user->getMaritalStatusUser() ? $user->getMaritalStatusUser()->getId() : ''" />
                        </td>
                    </tr>
                    <tr>
                        <td width="120" valign="top">
                            <span class="nobold">{_politViews}: </span>
                        </td>
                        <td>
                            <select name="politViews">
                                <option n:foreach="range(0, 9) as $i" n:attr="selected => ($user->getPoliticalViews() == $i)" value="{$i}">
                                    {tr("politViews_" . $i)}
                                </option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <td width="120" valign="top">
                            <span class="nobold">{_pronouns}: </span>
                        </td>
                        <td>
                            <select name="pronouns">
                                <option value="0" {if $user->getPronouns() == 0}selected{/if}>{_male}</option>
                                <option value="1" {if $user->getPronouns() == 1}selected{/if}>{_female}</option>
                                <option value="2" {if $user->getPronouns() == 2}selected{/if}>{_neutral}</option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <td width="120" valign="top">
                             <span class="nobold">{_birth_date}: </span>
                        </td>
                        <td>
                            <input max={date('Y-m-d')} name="birthday" value={is_null($user->getBirthday()) ? NULL : $user->getBirthday()->format('%Y-%m-%d')} type="date" style="margin-bottom: 7px;" />
                            <select name="birthday_privacy">
                                <option value="0" {if $user->getBirthdayPrivacy() == 0}selected{/if}>{_show_my_birthday}</option>
                                <option value="1" {if $user->getBirthdayPrivacy() == 1}selected{/if}>{_show_only_month_and_day}</option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <td width="120" valign="top">
                        </td>
                        <td>
                            <label><input type="checkbox" name="broadcast_music" n:attr="checked => $user->isBroadcastEnabled()">{_broadcast_audio}</label>
                        </td>
                    </tr>
                    <tr>
                        <td>

                        </td>
                        <td>
                            <input type="hidden" name="hash" value="{$csrfToken}" />
                            <input type="submit" value="{_save}" class="button" />
                        </td>
                    </tr>
                </tbody>
            </table>
        </form>

        {elseif $isContacts}

        <h4>{_contact_information}</h4>
        <form action="/edit?act=contacts" method="POST" enctype="multipart/form-data">
            <table cellspacing="7" cellpadding="0" width="60%" border="0" align="center">
                <tbody>
                    <tr>
                        <td width="120" valign="top">
                            <span class="nobold">{_email}: </span>
                        </td>
                        <td>
                            <input type="email" name="email_contact" value="{$user->getContactEmail()}" />
                        </td>
                    </tr>
                    <tr>
                        <td width="120" valign="top">
                            <span class="nobold">{_telegram}: </span>
                        </td>
                        <td>
                            <input type="text" name="telegram" value="{$user->getTelegram()}" />
                        </td>
                    </tr>
                    <tr>
                        <td width="120" valign="top">
                            <span class="nobold">{_personal_website}: </span>
                        </td>
                        <td>
                            <input type="text" name="website" value="{$user->getWebsite()}" />
                        </td>
                    </tr>
                    <tr>
                        <td width="120" valign="top">
                            <span class="nobold">{_city}: </span>
                        </td>
                        <td>
                            <input type="text" name="city" value="{$user->getCity()}" />
                        </td>
                    </tr>
                    <tr>
                        <td width="120" valign="top">
                            <span class="nobold">{_address}: </span>
                        </td>
                        <td>
                                <input type="text" name="address" value="{$user->getPhysicalAddress()}" />
                        </td>
                    </tr>
                    <tr>
                        <td>

                        </td>
                        <td>
                            <input type="hidden" name="hash" value="{$csrfToken}" />
                            <input type="submit" value="{_save}" class="button" />
                        </td>
                    </tr>
                </tbody>
            </table>
        </form>

        {elseif $isInterests}

        <h4>{_personal_information}</h4>
        <form action="/edit?act=interests" method="POST" enctype="multipart/form-data">
            <table cellspacing="7" cellpadding="0" width="60%" border="0" align="center">
                <tbody>
                    <tr>
                        <td width="120" valign="top">
                            <span class="nobold">{_interests}: </span>
                        </td>
                        <td>
                            <textarea type="text" name="interests">{$user->getInterests()}</textarea>
                        </td>
                    </tr>
                    <tr>
                        <td width="120" valign="top">
                            <span class="nobold">{_favorite_music}: </span>
                        </td>
                        <td>
                            <textarea type="text" name="fav_music">{$user->getFavoriteMusic()}</textarea>
                        </td>
                    </tr>
                    <tr>
                        <td width="120" valign="top">
                            <span class="nobold">{_favorite_films}: </span>
                        </td>
                        <td>
                            <textarea type="text" name="fav_films">{$user->getFavoriteFilms()}</textarea>
                        </td>
                    </tr>
                    <tr>
                        <td width="120" valign="top">
                            <span class="nobold">{_favorite_shows}: </span>
                        </td>
                        <td>
                            <textarea type="text" name="fav_shows">{$user->getFavoriteShows()}</textarea>
                        </td>
                    </tr>
                    <tr>
                        <td width="120" valign="top">
                            <span class="nobold">{_favorite_books}: </span>
                        </td>
                        <td>
                            <textarea type="text" name="fav_books">{$user->getFavoriteBooks()}</textarea>
                        </td>
                    </tr>
                    <tr>
                        <td width="120" valign="top">
                            <span class="nobold">{_favorite_quotes}: </span>
                        </td>
                        <td>
                            <textarea type="text" name="fav_quote">{$user->getFavoriteQuote()}</textarea>
                        </td>
                    </tr>
                    <tr>
                        <td width="120" valign="top">
                            <span class="nobold">{_favorite_games}: </span>
                        </td>
                        <td>
                            <textarea type="text" name="fav_games">{$user->getFavoriteGames()}</textarea>
                        </td>
                    </tr>
                    <tr>
                        <td width="120" valign="top">
                            <span class="nobold">{_information_about}: </span>
                        </td>
                        <td>
                            <textarea type="text" name="about">{$user->getDescription()}</textarea>
                        </td>
                    </tr>
                    <tr>
                        <td>

                        </td>
                        <td>
                            <input type="hidden" name="hash" value="{$csrfToken}" />
                            <input type="submit" value="{_save}" class="button" />
                        </td>
                    </tr>
                </tbody>
            </table>
        </form>

        {elseif $isAvatar}

        <h4>{_profile_picture}</h4>
        <form action="/al_avatars" method="POST" enctype="multipart/form-data">
            <table cellspacing="7" cellpadding="0" width="60%" border="0" align="center">
                <tbody>
                    <tr>
                        <td width="120" valign="top">
                            <span class="nobold">{_picture}: </span>
                        </td>
                        <td>
                            <label class="button" style="">{_browse}
                                <input type="file" id="blob" name="blob" style="display: none;" onchange="filename.innerHTML=blob.files[0].name" />
                            </label>
                            <div id="filename" style="margin-top: 10px;"></div>
                        </td>
                    </tr>
                    <tr>
                        <td>

                        </td>
                        <td>
                            <input type="hidden" name="hash" value="{$csrfToken}" />
                            <input type="submit" value="{_save}" class="button" />
                        </td>
                    </tr>
                </tbody>
            </table>
        </form>

        {elseif $isBackDrop}

        <h4>{_backdrop}</h4>
        <p>{_backdrop_desc}</p>
        <form method="POST" enctype="multipart/form-data">
            <div id="backdropEditor">
                <div id="backdropFilePicker">
                    <label class="button" style="">{_browse}<input type="file" accept="image/*" name="backdrop1" style="display: none;"></label>
                    <div id="spacer" style="width: 366px;"></div>
                    <label class="button" style="">{_browse}<input type="file" accept="image/*" name="backdrop2" style="display: none;"></label>
                    <div id="spacer" style="width: 366px;"></div>
                </div>
            </div>

            <p>
                <span class="nobold">{_backdrop_warn}</span>
            </p>
            <p>
                <span class="nobold">{_backdrop_about_adding}</span>
            </p>
            <p><br/></p>

            <input type="hidden" name="hash" value="{$csrfToken}" />
            <div>
                <center>
                    <button name="subact" value="save" class="button">{_backdrop_save}</button>
                    <button name="subact" value="remove" class="button">{_backdrop_remove}</button>
                </center>
            </div>
        </form>
        {elseif $isAdditional}
            {var $f_iterator = 0}
            <h4>{_additional_information}</h4>
            <p>{tr("additional_fields_description", ovkGetQuirk("users.max-fields"))}</p>
            <form id="additional_fields_form" method="POST" enctype="multipart/form-data">
                <div class="edit_field_container_inserts">
                    <table data-iterator="{$f_iterator}" class="outline_table edit_field_container_item" width="80%" border="0" align="center" n:foreach="$thisUser->getAdditionalFields() as $field">
                        <tbody>
                            <tr>
                                <td width="150">
                                    {_additional_field_name}
                                </td>
                                <td>
                                    <input name="name_{$f_iterator}" maxlength="50" type="text" value="{$field->getName(false)}">
                                </td>
                                <td>
                                    <div id="small_remove_button"></div>
                                </td>
                            </tr>
                            <tr>
                                <td valign="top">
                                    {_additional_field_text}
                                </td>
                                <td>
                                    <textarea name="text_{$f_iterator}" maxlength="1000">{$field->getContent()}</textarea>
                                </td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>
                                    {_additional_field_place}
                                </td>
                                <td>
                                    <select name="place_{$f_iterator}">
                                        <option value="0" n:attr="selected => $field->isContact()">{_additional_field_place_contacts}</option>
                                        <option value="1" n:attr="selected => !$field->isContact()">{_additional_field_place_interests}</option>
                                    </select>
                                </td>
                                <td></td>
                            </tr>
                        </tbody>

                        {php $f_iterator += 1}
                    </table>
                </div>

                <div class="flex_column_center_gap5px">
                    <input type="button" id="additional_field_append" value="{_add}" class="button" />
                    <input type="hidden" name="hash" value="{$csrfToken}" />
                    <input type="submit" value="{_save}" class="button" />
                </div>
            </form>
        {/if}
    </div>

{/block}
