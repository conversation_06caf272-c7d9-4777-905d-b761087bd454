{extends "../@layout.xml"}

{if !$user->isBanned()}
    {var $backdrops = $user->getBackDropPictureURLs()}
{/if}

{block title}{$user->getCanonicalName()}{/block} 

{block headIncludes}
    {if $user->getPrivacyPermission('page.read', $thisUser ?? NULL)}
    <!-- openGraph -->
    <meta property="og:title" content="{$user->getCanonicalName()}" />
    <meta property="og:url" content="http://{$_SERVER['HTTP_HOST']}{$user->getURL()}" />
    <meta property="og:image" content="{$user->getAvatarUrl('normal')}" />
    <meta property="og:type" content="profile" />
    <meta property="og:first_name" content="{$user->getFirstName()}" />
    <meta property="og:last_name" content="{$user->getLastName()}" />
    <meta n:if="!is_null($user->getShortcode())" property="og:username" content="{$user->getShortcode()}" />
    <meta property="og:gender" content="{($user->isFemale() ? 'fe' : '')}male" />
    
    <!-- json-ld -->
    <script type="application/ld+json">
        {
            "@context": "http://schema.org/",
            "type": "Person",
            "name": {$user->getCanonicalName()},
            "url": {('http://') . $_SERVER['HTTP_HOST'] . $user->getURL()}
        }
    </script>
    {else} 
    <meta name="robots" content="noindex, noarchive">
    {/if}
{/block}

{block header}
    {$user->getCanonicalName()}
    <img n:if="$user->isVerified()"
         class="name-checkmark"
         src="/assets/packages/static/openvk/img/checkmark.png"
         />
    <span n:if="isset($thisUser) && $user->getId() == $thisUser->getId()"><b>({_this_is_you})</b></span>
    
    <!-- DEBUG: ONLINE REPORT: static {$user->getOnline()->timestamp()}s adjusted {$user->getOnline()->timestamp() + 2505600}s real {time()}s -->
    <div n:if="$user->getOnline()->timestamp() + 2505600 > time()" style="float:right;">
        {if $user->isOnline()}
            <span><b>{_online}</b></span>
        {else}
            <span>{$user->isFemale() ? tr("was_online_f") : tr("was_online_m")} {$user->getOnline()}</span>
        {/if}
        {var $platform = $user->getOnlinePlatform()}
        {var $platformDetails = $user->getOnlinePlatformDetails()}
        <a n:if="!empty($platform)" class="client_app client_app_titlebar" data-app-tag="{$platform}" data-app-name="{$platformDetails['name']}" data-app-url="{$platformDetails['url']}" data-app-img="{$platformDetails['img']}">
            <img src="/assets/packages/static/openvk/img/app_icons_mini/{$user->getOnlinePlatform(this)}.svg">
        </a>
    </div>
    <div n:if="$user->onlineStatus() == 2" style="float:right;">
        <span><b>{_deceased_person}</b></span>
    </div>
{/block}

{block content}
    {if !$user->isBanned()}
    
    {if !$user->getPrivacyPermission('page.read', $thisUser ?? NULL)}
        <div class="msg msg_err">
            <b>{_forbidden}</b><br/>
            {_forbidden_comment}
        </div>
    {else}
    
    <div class="left_small_block">
        <div class="avatar_block">
            {var $hasAvatar = !str_contains($user->getAvatarUrl('miniscule'), "/assets/packages/static/openvk/img/camera_200.png")}
            
            {if $thisUser && $user->getId() == $thisUser->getId()}
                <a {if $hasAvatar}style="display:none"{/if} class="add_image_text" id="add_image">{_add_image}</a>
                <div {if !$hasAvatar}style="display:none"{/if} class="avatar_controls">
                    <div class="avatarDelete hoverable"></div>
                    <div class="avatar_variants">
                        <a class="_add_image hoverable" id="add_image"><span>{_upload_new_picture}</span></a>
                    </div>
                </div>
            {/if}
            <a href="{$user->getAvatarLink()|nocheck}">
                <img src="{$user->getAvatarUrl('normal')}"
                     alt="{$user->getCanonicalName()}"
                     id="bigAvatar"
                     style="width: 100%; image-rendering: -webkit-optimize-contrast;" />
            </a>
        </div>
        <div n:ifset="$thisUser" id="profile_links">
            {if $user->getId() == $thisUser->getId()}
                <div id="profile_link" style="width: 194px;">
                    <a href="/edit" class="link">{_edit_page}</a>
                </div>
                <div n:if="OPENVK_ROOT_CONF['openvk']['preferences']['commerce'] && !$thisUser->prefersNotToSeeRating()" id="profile_link" style="width: 194px;">
                    <a onClick="showIncreaseRatingDialog({$thisUser->getCoins()}, {ltrim($thisUser->getUrl(), '/')}, {$csrfToken})" class="link">{_increase_rating}</a>
                </div>
            {else}
                {if $thisUser->getChandlerUser()->can("substitute")->model('openvk\Web\Models\Entities\User')->whichBelongsTo(0)}
                    <a href="/setSID/{$user->getChandlerUser()->getId()}?hash={rawurlencode($csrfToken)}" class="profile_link" style="width: 194px;">
                        {tr("login_as", $user->getFirstName())}
                    </a>
                {/if}
                
                {if $thisUser->getChandlerUser()->can("access")->model("admin")->whichBelongsTo(NULL)}
                    <a href="/admin/users/id{$user->getId()}" class="profile_link" style="width: 194px;">
                        {_manage_user_action}
                    </a>
                    <a href="javascript:banUser()" class="profile_link" style="width: 194px;">
                        {_ban_user_action}
                    </a>
                    <a href="javascript:warnUser()" class="profile_link" style="width: 194px;">
                        {_warn_user_action}
                    </a>
                    <a href="/admin/user{$user->getId()}/bans" class="profile_link" rel="nofollow">
                        {_blocks}
                    </a>
                    <a href="/admin/logs?uid={$user->getId()}" class="profile_link" style="width: 194px;" rel="nofollow">
                        {_last_actions}
                    </a>
                {/if}

                {if $thisUser->getChandlerUser()->can('write')->model('openvk\Web\Models\Entities\TicketReply')->whichBelongsTo(0)}
                    <a href="javascript:toggleBanInSupport()" class="profile_link" style="width: 194px;">
                        {if $user->isBannedInSupport()}
                            {_unban_in_support_user_action}
                        {else}
                            {_ban_in_support_user_action}
                        {/if}
                    </a>
                {/if}
                
                <a style="width: 194px;" n:if="OPENVK_ROOT_CONF['openvk']['preferences']['commerce'] && $user->getGiftCount() == 0" href="/gifts?act=pick&user={$user->getId()}" class="profile_link">{_send_gift}</a>
                <a style="width: 194px;" n:if="$user->getPrivacyPermission('messages.write', $thisUser)" href="/im?sel={$user->getId()}" class="profile_link" rel="nofollow">{_send_message}</a>

                {var $subStatus = $user->getSubscriptionStatus($thisUser)}
                {if $subStatus === 0}
                    <form action="/setSub/user" method="post" class="profile_link_form">
                        <input type="hidden" name="act" value="add" />
                        <input type="hidden" name="id"  value="{$user->getId()}" />
                        <input type="hidden" name="hash" value="{$csrfToken}" />
                        <input type="submit" class="profile_link" value="{_friends_add}" style="width: 194px;" />
                    </form>
                {elseif $subStatus === 1}
                    <form action="/setSub/user" method="post" class="profile_link_form">
                        <input type="hidden" name="act" value="add" />
                        <input type="hidden" name="id"  value="{$user->getId()}" />
                        <input type="hidden" name="hash" value="{$csrfToken}" />
                        <input type="submit" class="profile_link" value="{_friends_accept}" style="width: 194px;" />
                    </form>
                {elseif $subStatus === 2}
                    <form action="/setSub/user" method="post" class="profile_link_form">
                        <input type="hidden" name="act" value="rem" />
                        <input type="hidden" name="id"  value="{$user->getId()}" />
                        <input type="hidden" name="hash" value="{$csrfToken}" />
                        <input type="submit" class="profile_link" value="{_friends_reject}" style="width: 194px;" />
                    </form>
                {elseif $subStatus === 3}
                    <form action="/setSub/user" method="post" class="profile_link_form">
                        <input type="hidden" name="act" value="rem" />
                        <input type="hidden" name="id"  value="{$user->getId()}" />
                        <input type="hidden" name="hash" value="{$csrfToken}" />
                        <input type="submit" class="profile_link" value="{_friends_delete}" style="width: 194px;" />
                    </form>
                {/if}

                <a n:if="!$blacklist_status" id="_bl_toggler" data-name="{$user->getMorphedName('genitive', false)}" data-val="1" data-id="{$user->getRealId()}" class="profile_link" style="display:block;width:96%;">{_bl_add}</a>
                {* 4 admins *}
                <a n:if="$blacklist_status" id="_bl_toggler" data-val="0" data-id="{$user->getRealId()}" class="profile_link" style="display:block;width:96%;">{_bl_remove}</a>
                <a class="profile_link" style="display:block;width:96%;" href="javascript:reportUser({$user->getId()})">{_report}</a>
                <a n:if="!$user->isHideFromGlobalFeedEnabled()" class="profile_link" style="display:block;width:96%;" id="__ignoreSomeone" data-val='{!$ignore_status ? 1 : 0}' data-id="{$user->getId()}">
                    {if !$ignore_status}{_ignore_user}{else}{_unignore_user}{/if}
                </a>
            {/if}
            <a style="width: 194px;" n:if="$user->getFollowersCount() > 0" href="/friends{$user->getId()}?act=incoming" class="profile_link">{tr("followers", $user->getFollowersCount())}</a>
        </div>
        <div n:if="isset($thisUser) && !$thisUser->prefersNotToSeeRating()" class="profile-hints">
            {var $completeness = $user->getProfileCompletenessReport()}
            
            <div n:class="completeness-gauge, $completeness->total >= 100 ? completeness-gauge-gold">
                <div style="width: {$completeness->percent}%"></div>
                <span>{$completeness->total}%</span>
            </div>
            
            {if isset($thisUser) && $user->getId() === $thisUser->getId() && sizeof($completeness->unfilled) > 0}
                <br/>
                <a n:if="in_array('interests', $completeness->unfilled)" href="/edit?act=interests">
                    <img src="/assets/packages/static/openvk/img/icon1.gif" />
                    {_interests} (+20%)
                </a>
                <a n:if="in_array('email', $completeness->unfilled)" href="/edit?act=contacts">
                    <img src="/assets/packages/static/openvk/img/icon2.gif" />
                    Email (+20%)
                </a>
                <a n:if="in_array('phone', $completeness->unfilled)" href="/edit?act=contacts">
                    <img src="/assets/packages/static/openvk/img/icon2.gif" />
                    {_phone} (+20%)
                </a>
                <a n:if="in_array('telegram', $completeness->unfilled)" href="/edit?act=contacts">
                    <img src="/assets/packages/static/openvk/img/icon2.gif" />
                    Telegram (+15%)
                </a>
                <a n:if="in_array('status', $completeness->unfilled)" href="/edit">
                    <img src="/assets/packages/static/openvk/img/icon3.gif" />
                    {_status} (+15%)
                </a>
            {/if}
        </div>
        <br />
        <div n:if="$user->getFriendsCount() > 0 && $user->getPrivacyPermission('friends.read', $thisUser ?? NULL)">
            {var $friendCount = $user->getFriendsCount()}
            
            <div class="content_title_expanded" onclick="hidePanel(this, {$friendCount});">
                {_friends}
            </div>
            <div>
                <div class="content_subtitle">
                    {tr("friends", $friendCount)}
                    <div style="float:right;">
                        <a href="/friends{$user->getId()}">{_all_title}</a>
                    </div>
                </div>
                <div class="content_list">
                    <div class="cl_element" n:foreach="$user->getFriends(1) as $friend">
                        <div class="cl_avatar">
                            <a href="{$friend->getURL()}">
                                <img class="ava" src="{$friend->getAvatarUrl('miniscule')}" />
                            </a>
                        </div>
                        <a href="{$friend->getURL()}" class="cl_name">
                            <text class="cl_fname">{$friend->getFirstName()}</text>
                            <text class="cl_lname">{$friend->getLastName()}</text>
                        </a>    
                    </div>
                </div>
            </div>
        </div>
        <div n:if="$thisUser != NULL && $thisUser->getId() == $user->getId() && $user->getFriendsOnlineCount() > 0">
            {var $friendOnlineCount = $user->getFriendsOnlineCount()}
            
            <div class="content_title_expanded" onclick="hidePanel(this, {$friendOnlineCount});">
                {_friends_online}
            </div>
            <div>
                <div class="content_subtitle">
                    {tr("friends_online", $friendOnlineCount)}
                    <div style="float:right;">
                        <a href="/friends{$user->getId()}?act=online">{_all_title}</a>
                    </div>
                </div>
                <div class="content_list">
                    <div class="cl_element" n:foreach="$user->getFriendsOnline(1) as $friend">
                        <div class="cl_avatar">
                            <a href="{$friend->getURL()}">
                                <img class="ava" src="{$friend->getAvatarUrl('miniscule')}" />
                            </a>
                        </div>
                        <a href="{$friend->getURL()}" class="cl_name">
                            <text class="cl_fname">{$friend->getFirstName()}</text>
                            <text class="cl_lname">{$friend->getLastName()}</text>
                        </a>    
                    </div>
                </div>
            </div>
        </div>
        <div n:if="$albumsCount > 0 && $user->getPrivacyPermission('photos.read', $thisUser ?? NULL)">
            <div class="content_title_expanded" onclick="hidePanel(this, {$albumsCount});">
                {_albums}
            </div>
            <div>
                <div class="content_subtitle">
                    {tr("albums", $albumsCount)}
                    <div style="float:right;">
                        <a href="/albums{$user->getId()}">{_all_title}</a>
                    </div>
                </div>
                <div style="padding: 5px;">
                    <div class="ovk-album" style="display: inline-block;" n:foreach="$albums as $album">
                        <div style="text-align: center;float: left;height: 54pt;width: 100px;">
                            {var $cover = $album->getCoverPhoto()}
                            
                            <img
                                src="{is_null($cover)?'/assets/packages/static/openvk/img/camera_200.png':$cover->getURLBySizeId('small')}"
                                style="max-width: 80px; max-height: 54pt;" loading=lazy />
                        </div>
                        <div style="overflow: hidden; overflow-wrap: break-word;">
                            <b><a href="/album{$album->getPrettyId()}">{$album->getName()}</a></b><br>
                            <span class="nobold">{tr("updated_at", $album->getEditTime() ?? $album->getPublicationTime())}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div n:if="$videosCount > 0 && $user->getPrivacyPermission('videos.read', $thisUser ?? NULL)">
            <div class="content_title_expanded" onclick="hidePanel(this, {$videosCount});">
                {_videos}
            </div>
            <div>
                <div class="content_subtitle">
                    {tr("videos", $videosCount)}
                    <div style="float:right;">
                        <a href="/videos{$user->getId()}">{_all_title}</a>
                    </div>
                </div>
                <div style="padding: 5px;">
                    <div class="ovk-video" n:foreach="$videos as $video">
                        <a href="/video{$video->getPrettyId()}" class="preview" align="center" id="videoOpen" data-id="{$video->getPrettyId()}">
                            <img
                                src="{$video->getThumbnailURL()}"
                                style="max-width: 170px; max-height: 127px; margin: auto;" />
                        </a>
                        <div>
                            <b><a href="/video{$video->getPrettyId()}" id="videoOpen" data-id="{$video->getPrettyId()}">{ovk_proc_strtr($video->getName(), 30)}</a></b><br>
                            <span style="font-size: 10px;">{$video->getPublicationTime()} | {_comments} ({$video->getCommentsCount()})</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div n:if="$notesCount > 0 && $user->getPrivacyPermission('notes.read', $thisUser ?? NULL)">
            <div class="content_title_expanded" onclick="hidePanel(this, {$notesCount});">
                {_notes}
            </div>
            <div>
                <div class="content_subtitle">
                    {tr("notes", $notesCount)}
                    <div style="float:right;">
                        <a href="/notes{$user->getId()}">{_all_title}</a>
                    </div>
                </div>
				
				<div style="padding: 5px 8px 15px 8px;">
                    <ul class="notes_titles" n:foreach="$notes as $note">
						<li class="written">
							<a href="/note{$note->getPrettyId()}">
								{$note->getName()}
							</a>
							<small>
								{$note->getPublicationTime()}
								<span class="divide">|</span>
								<a href="/note{$note->getPrettyId()}">{_comments}</a>
							</small>
						</li>
					</ul>
                </div>
            </div>
        </div>
        <div n:if="$user->getClubCount() > 0 && $user->getPrivacyPermission('groups.read', $thisUser ?? NULL)">
            {var $clubsCount = $user->getClubCount()}
            <div class="content_title_expanded" onclick="hidePanel(this, {$clubsCount})">
                {_groups}
            </div>
            <div>
                <div class="content_subtitle">
                    {tr("groups", $clubsCount)}
                    <div style="float:right;">
                        <a href="/groups{$user->getId()}">{_all_title}</a>
                    </div>
                </div>
                <div style="padding-left: 5px;">
                    <div style="display: inline;" n:foreach="$user->getClubs(1) as $club">
                        <a href="club{$club->getId()}">{$club->getName()}</a> {if !$iterator->last}•{/if}
                    </div>
                </div>
            </div>
        </div>
        <div n:if="$user->getMeetingCount() > 0 && $user->getPrivacyPermission('groups.read', $thisUser ?? NULL)">
            {var $meetingCount = $user->getMeetingCount()}
            <div class="content_title_expanded" onclick="hidePanel(this, {$meetingCount})">
                {_meetings}
            </div>
            <div>
                <div class="content_subtitle">
                    {tr("meetings", $meetingCount)}
                    <div style="float:right;">
                        <a href="/groups{$user->getId()}?act=meetings">{_all_title}</a>
                    </div>
                </div>
                <div style="padding-left: 5px;">
                    <div style="display: inline;" n:foreach="$user->getMeetings(1) as $meeting">
                        <a href="/event{$meeting->getId()}">{$meeting->getName()}</a> {if !$iterator->last}•{/if}
                    </div>
                </div>
            </div>
        </div>
        
    </div>
    
    <div class="right_big_block">
        <div class="page_info">
            <div n:if="!is_null($alert = $user->getAlert())" class="user-alert">{strpos($alert, "@") === 0 ? tr(substr($alert, 1)) : $alert}</div>
            {var $thatIsThisUser = isset($thisUser) && $user->getId() == $thisUser->getId()}
            <div n:if="$thatIsThisUser" class="page_status_popup" id="status_editor" style="display: none;">
                <form name="status_popup_form" onsubmit="changeStatus(); return false;">
                    <div style="margin-bottom: 10px;">
                        <input type="text" name="status" size="50" value="{$user->getStatus()}" />
                        <label style="width: 316px;display: block;">
                            <input type="checkbox" name="broadcast" n:attr="checked => $user->isBroadcastEnabled()" />
                            {_broadcast_audio}
                        </label>
                    </div>
                    <input type="hidden" name="hash" value="{$csrfToken}" />
                    <button type="submit" name="submit" class="button" style="height: 22px;">{_send}</button>
                </form>
            </div>
			<div class="accountInfo clearFix">
                <div class="profileName">
                    <h2>{$user->getFullName()}</h2>

                    {if !$audioStatus}
                        {if !is_null($user->getStatus())}
                            <div n:class="page_status, $thatIsThisUser ? page_status_edit_button" n:attr="id => $thatIsThisUser ? page_status_text : NULL">{$user->getStatus()}</div>
                        {elseif $thatIsThisUser}
                            <div class="page_status">
                                <div n:class="edit_link, $thatIsThisUser ? page_status_edit_button" id="page_status_text">[ {_change_status} ]</div>
                            </div>
                        {/if}
                    {else}
                        <div class="page_status" style="display: flex;">
                            <div n:class="audioStatus, $thatIsThisUser ? page_status_edit_button" id="page_status_text">
                                <a {if !$thatIsThisUser}href="/audio0_{$audioStatus->getId()}"{/if}>{$audioStatus->getName()}</a>
                            </div>
                        </div>
                    {/if}
                </div>
            </div>
            <div>
                <table id="basicInfo" class="ugc-table" border="0" cellspacing="0" cellpadding="0" border="0" cellspacing="0" cellpadding="0" n:if=" $user->getPrivacyPermission('page.info.read', $thisUser ?? NULL)">
                    <tbody>
                        <tr>
                            <td class="label"><span class="nobold">{_pronouns}: </span></td>
                            <td class="data">{$user->isFemale() ? tr("female") : ($user->isNeutral() ? tr("neutral") : tr("male"))}</td>
                        </tr>
                        <tr>
                            <td class="label"><span class="nobold">{_relationship}:</span></td>
                            <td class="data">
                                {$user->getLocalizedMaritalStatus()}
                                {if $user->getMaritalStatusUser()}
                                    {$user->getMaritalStatusUserPrefix()}
                                    <a href="{$user->getMaritalStatusUser()->getURL()}" target="_blank">
                                        {$user->getMaritalStatusUser()->getCanonicalName()}
                                    </a>
                                {/if}
                            </td>
                        </tr>
                        <tr>
                            <td class="label"><span class="nobold">{_registration_date}: </span></td>
                            <td class="data">{$user->getRegistrationTime()}</td>
                        </tr>
                        <tr n:if="!is_null($user->getHometown())">
                            <td class="label"><span class="nobold">{_hometown}:</span></td>
                            <td class="data"><a href="/search?section=users&q=&hometown={urlencode($user->getHometown())}">{$user->getHometown()}</a></td>
                        </tr>
                        <tr>
                            <td class="label"><span class="nobold">{_politViews}:</span></td>
                            <td class="data">{var $pviews = $user->getPoliticalViews()}{_"politViews_$pviews"}</td>
                        </tr>
                        <tr n:if="!is_null($user->getBirthday())">
                            <td class="label"><span class="nobold">{_birth_date}:</span></td>
                            <td n:if="$user->getBirthdayPrivacy() == 0" class="data">{$user->getBirthday()->format('%e %B %Y')}{if $user->onlineStatus() != 2},
                                {tr("years", $user->getAge())}{/if}</td>
                            <td n:if="$user->getBirthdayPrivacy() == 1" class="data">{$user->getBirthday()->format('%e %B')}</td>
                        </tr>
                    </tbody>
                </table>
			</div>
        </div>
        <div n:if="$user->getPrivacyPermission('page.info.read', $thisUser ?? NULL)">
            <div class="content_title_expanded" onclick="hidePanel(this);">
                {_information}
            </div>
            
            {capture $contactInfo_Tmp}
                <table class="ugc-table" border="0" cellspacing="0" cellpadding="0" border="0" cellspacing="0" cellpadding="0" n:ifcontent>
                  <tbody n:ifcontent>
                        <tr n:if="!is_null($user->getContactEmail())">
                            <td class="label"><span class="nobold">{_email}: </span></td>
                            <td>
                                <a href="mailto:{$user->getContactEmail()}" rel="ugc">
                                    {$user->getContactEmail()}
                                </a>
                            </td>
                        </tr>
                        <tr n:if="!is_null($user->getTelegram())">
                            <td class="label"><span class="nobold">{_telegram}: </span></td>
                            <td>
                                <a href="https://t.me/{$user->getTelegram()}" rel="ugc" target="_blank">
                                    @{$user->getTelegram()}
                                </a>
                            </td>
                        </tr>
                        <tr n:if="!is_null($user->getWebsite())">
                            <td class="label"><span class="nobold">{_personal_website}: </span></td>
                            <td>
                                <a href="{$user->getWebsite()}" rel="ugc" target="_blank">
                                    {$user->getWebsite()}
                                </a>
                            </td>
                        </tr>
                    <tr n:if="!is_null($user->getCity())">
                        <td class="label"><span class="nobold">{_city}:</span></td>
                        <td class="data"><a href="/search?type=section&q=&city={$user->getCity()}">{$user->getCity()}</a></td>
                    </tr>
                    <tr n:if="!is_null($user->getPhysicalAddress())">
                        <td class="label"><span class="nobold">{_address}:</span></td>
                        <td class="data">{$user->getPhysicalAddress()}</td>
                    </tr>
                    {if $additionalFields}
                        <tr n:foreach="$additionalFields['contacts'] as $field">
                            <td class="label"><span class="nobold">{$field->getName()}:</span></td>
                            <td class="data">{$field->getContent()}</td>
                        </tr>
                    {/if}
                  </tbody>
                </table>
            {/capture}
            {capture $uInfo_Tmp}
                <table class="ugc-table" border="0" cellspacing="0" cellpadding="0" border="0" cellspacing="0" cellpadding="0" n:ifcontent>
                  <tbody n:ifcontent>
                    <tr n:if="!is_null($user->getInterests())">
                        <td class="label"><span class="nobold">{_interests}: </span></td>
                        <td class="data">
                            {var $interests = explode(", ", $user->getInterests())}
                            
                            {foreach $interests as $interest}
                                <span>{$interest}</span>{if $interest != end($interests)},{/if}
                            {/foreach}
                        </td>
                    </tr>
                    <tr n:if="!is_null($user->getFavoriteMusic())">

                        <td class="label"><span class="nobold">{_favorite_music}: </span></td>
                        <td class="data">
                            {var $musics = explode(", ", $user->getFavoriteMusic())}

                            {foreach $musics as $music}
                                <a href="/search?section=audios&q={urlencode($music)}">{$music}</a>{if $music != end($musics)},{/if}
                            {/foreach}
                        </td>
                    </tr>
                    <tr n:if="!is_null($user->getFavoriteFilms())">
                        <td class="label"><span class="nobold">{_favorite_films}: </span></td>
                        <td class="data">
                            {var $films = explode(", ", $user->getFavoriteFilms())}

                            {foreach $films as $film}
                                <a href="/search?section=users&q=&fav_films={urlencode($film)}">{$film}</a>{if $film != end($films)},{/if}
                            {/foreach}
                        </td>
                    </tr>
                    <tr n:if="!is_null($user->getFavoriteShows())">
                        <td class="label"><span class="nobold">{_favorite_shows}: </span></td>
                        <td class="data">                        
                            {var $shows = explode(", ", $user->getFavoriteShows())}

                            {foreach $shows as $show}
                                <a href="/search?section=users&q=&fav_shows={urlencode($show)}">{$show}</a>{if $show != end($shows)},{/if}
                            {/foreach}
                        </td>
                    </tr>
                    <tr n:if="!is_null($user->getFavoriteBooks())">
                        <td class="label"><span class="nobold">{_favorite_books}: </span></td>
                        <td class="data">
                            {var $books = explode(", ", $user->getFavoriteBooks())}

                            {foreach $books as $book}
                                <a href="/search?section=users&q=&fav_books={urlencode($book)}">{$book}</a>{if $book != end($books)},{/if}
                            {/foreach}
                        </td>
                    </tr>
                    <tr n:if="!is_null($user->getFavoriteQuote())">
                        <td class="label"><span class="nobold">{_favorite_quotes}: </span></td>
                        <td class="data">{$user->getFavoriteQuote()}</td>
                    </tr>
                    <tr n:if="!is_null($user->getFavoriteGames())">
                        <td class="label"><span class="nobold">{_favorite_games}: </span></td>
                        <td class="data">{$user->getFavoriteGames()}</td>
                    </tr>
                    {if $additionalFields}
                        <tr n:foreach="$additionalFields['interests'] as $field">
                            <td class="label"><span class="nobold">{$field->getName()}:</span></td>
                            <td class="data">{$field->getContent()}</td>
                        </tr>
                    {/if}
                    <tr n:if="!is_null($user->getDescription())">
                        <td class="label"><span class="nobold">{_information_about}: </span></td>
                        <td class="data">{$user->getDescription()}</td>
                    </tr>
                  </tbody>
                </table>
            {/capture}
            <div>
            <div style="padding: 10px 8px 15px 8px;" n:ifcontent>
                <h4 style="border-bottom: none; font-size: 11px; padding: 0; display: inline-block;">{_contact_information} {ifset $thisUser}{if $thisUser->getId() == $user->getId()}<a href="/edit?act=contacts" class="edit_link">[ {_edit} ]</a>{/if}{/ifset}</h4>
                {if !empty($contactInfo_Tmp)}
                    {$contactInfo_Tmp|noescape}
                {else}
                    <div style="padding: 15px;color:gray;text-align: center;">{_no_information_provided}</div>
                {/if}
                <br>
                <h4 style="border-bottom: none; font-size: 11px; padding: 0; display: inline-block;">{_personal_information} {ifset $thisUser}{if $thisUser->getId() == $user->getId()}<a href="/edit?act=interests" class="edit_link">[ {_edit} ]</a>{/if}{/ifset}</h4>
                {if !empty($uInfo_Tmp)}
                    {$uInfo_Tmp|noescape}
                {else}
                    <div style="padding-top: 15px;color:gray;text-align: center;">{_no_information_provided}</div>
                {/if}
            </div>
            </div>
        </div>

        <div n:if="$audiosCount > 0 && $user->getPrivacyPermission('audios.read', $thisUser ?? NULL)">
            <div class="content_title_expanded" onclick="hidePanel(this, {$audiosCount});">
                {_audios}
            </div>
            <div>
                <div class="content_subtitle">
                    {tr("audios_count", $audiosCount)}
                    <div style="float:right;">
                        <a href="/audios{$user->getId()}">{_all_title}</a>
                    </div>
                </div>
                <div class="content_list long">
                    <div class="audio" n:foreach="$audios as $audio" style="width: 100%;">
                        {include "../Audio/player.xml", audio => $audio}
                    </div>
                </div>
            </div>
        </div>

        <div n:if="OPENVK_ROOT_CONF['openvk']['preferences']['commerce'] && ($giftCount = $user->getGiftCount()) > 0">
            <div class="content_title_expanded" onclick="hidePanel(this, {$giftCount});">
                {_gifts}
            </div>
            <div>
                <div class="content_subtitle">
                    {tr("gifts", $giftCount)}
                    <div style="float:right;">
                        {if OPENVK_ROOT_CONF['openvk']['preferences']['commerce'] && !is_null($thisUser)}
                            <a href="/gifts?act=pick&user={$user->getId()}">{_send_gift}</a> |
                        {/if}
                        <a href="/gifts{$user->getId()}">{_all_title}</a>
                    </div>
                </div>
                <div class="content_list long">
                    <div class="cl_element" style="width: 25%;" n:foreach="$user->getGifts(1, 4) as $giftDescriptor">
                        {var $hideInfo = !is_null($thisUser) ? ($giftDescriptor->anon ? $thisUser->getId() !== $user->getId() : false) : false}
                        <div class="cl_avatar">
                            <a href="{$hideInfo ? 'javascript:false' : $giftDescriptor->sender->getURL()}">
                                <img style="width: 70px; max-height: 70px;"
                                    src="{$giftDescriptor->gift->getImage(2)}"
                                    alt="{$hideInfo ? tr('gift') : ($giftDescriptor->caption ?? tr('gift'))}" 
                                    title="{$hideInfo ? tr('gift') : ($giftDescriptor->caption ?? tr('gift'))}" loading=lazy />
                            </a>  
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        {presenter "openvk!Wall->wallEmbedded", $user->getId()}
        
        <script n:if="isset($thisUser) && $thisUser->getChandlerUser()->can('access')->model('admin')->whichBelongsTo(NULL)">
            function banUser() {
                uBanMsgTxt  = "Вы собираетесь забанить пользователя " + {$user->getCanonicalName()} + ".";
                uBanMsgTxt += "<br/><b>Предупреждение</b>: Это действие удалит все подписки пользователя и отпишет всех от него.";
                uBanMsgTxt += "<br/><br/><b>Причина бана</b>: <input type='text' id='uBanMsgInput' placeholder='придумайте что-нибудь крутое' />"
                uBanMsgTxt += "<br/><br/><b>Заблокировать до</b>: <input type='date' id='uBanMsgDate' />";
                uBanMsgTxt += "<br/><br/><input id='uBanMsgIncr' type='checkbox' checked='1'/>Автоматически <b>(до " + {date('d.m.Y H\h', time() + $user->getNewBanTime())} + ")</b>";

                MessageBox("Забанить " + {$user->getFirstName()}, uBanMsgTxt, ["Подтвердить", "Отмена"], [
                    (function() {
                        res = document.querySelector("#uBanMsgInput").value;
                        date = document.querySelector("#uBanMsgDate").value;
                        incr = document.querySelector("#uBanMsgIncr").checked ? '1' : '0';
                        xhr = new XMLHttpRequest();
                        xhr.open("GET", "/admin/ban/" + {$user->getId()} + "?reason=" + res + "&incr=" + incr + "&date=" + date + "&hash=" + {rawurlencode($csrfToken)}, true);
                        xhr.onload = (function() {
                            if(xhr.responseText.indexOf("success") === -1)
                                MessageBox("Ошибка", "Не удалось забанить пользователя...", ["OK"], [Function.noop]);
                            else
                                MessageBox("Операция успешна", "Пользователь заблокирован", ["OK"], [Function.noop]);
                        });
                        xhr.send(null);
                    }),
                    Function.noop
                ]);
            }
            
            function warnUser() {
                uBanMsgTxt  = "Вы собираетесь предупредить пользователя " + {$user->getCanonicalName()} + ".";
                uBanMsgTxt += "<br/>Мы отправим уведомление пользователю в личные сообщения от имени аккаунта администратора.";
                uBanMsgTxt += "<br/><br/><b>Текст предупреждения</b>: <input type='text' id='uWarnMsgInput' placeholder='придумайте что-нибудь крутое' />";
                
                MessageBox("Выдать предупреждение " + {$user->getFirstName()}, uBanMsgTxt, ["Подтвердить", "Отмена"], [
                    (function() {
                        res = document.querySelector("#uWarnMsgInput").value;
                        xhr = new XMLHttpRequest();
                        xhr.open("GET", "/admin/warn/" + {$user->getId()} + "?message=" + res + "&hash=" + {rawurlencode($csrfToken)}, true);
                        xhr.onload = (function() {
                            if(xhr.responseText.indexOf("message") === -1)
                                MessageBox("Ошибка", "Не удалось отправить предупреждение...", ["OK"], [Function.noop]);
                            else
                                MessageBox("Операция успешна", "Предупреждение отправлено", ["OK"], [Function.noop]);
                        });
                        xhr.send(null);
                    }),
                    Function.noop
                ]);
            }
        </script>

        <script n:if="isset($thisUser) && $thisUser->getChandlerUser()->can('write')->model('openvk\Web\Models\Entities\TicketReply')->whichBelongsTo(0)">
            {if $user->isBannedInSupport()}
                function toggleBanInSupport() {
                    uBanMsgTxt  = "Вы собираетесь разблокировать в поддержке пользователя " + {$user->getCanonicalName()} + ".";
                    uBanMsgTxt += "<br/>Сейчас он заблокирован по причине <strong>" + {$user->getBanInSupportReason()} + "</strong>.";

                    MessageBox("Разблокировать в поддержке " + {$user->getFirstName()}, uBanMsgTxt, ["Подтвердить", "Отмена"], [
                        (function() {
                            xhr = new XMLHttpRequest();
                            xhr.open("GET", "/admin/support/unban/" + {$user->getId()} + "?hash=" + {rawurlencode($csrfToken)}, true);
                            xhr.onload = (function() {
                                if(xhr.responseText.indexOf("success") === -1)
                                    MessageBox("Ошибка", "Не удалось разблокировать пользователя в поддержке...", ["OK"], [Function.noop]);
                                else
                                    MessageBox("Операция успешна", "Пользователь разблокирован в поддержке", ["OK"], [Function.noop]);
                            });
                            xhr.send(null);
                        }),
                        Function.noop
                    ]);
                }
            {else}
                function toggleBanInSupport() {
                    uBanMsgTxt  = "Вы собираетесь заблокировать в поддержке пользователя " + {$user->getCanonicalName()} + ".";
                    uBanMsgTxt += "<br/><br/><b>Причина бана</b>: <input type='text' id='uBanMsgInput' placeholder='придумайте что-нибудь крутое' />";
                    uBanMsgTxt += "<br/><br/><input type='checkbox' id='uBanClsTicketsInput' /><label for='uBanClsTicketsInput'>Закрыть все обращения пользователя</label>";

                    MessageBox("Заблокировать в поддержке " + {$user->getFirstName()}, uBanMsgTxt, ["Подтвердить", "Отмена"], [
                        (function() {
                            res = document.querySelector("#uBanMsgInput").value;
                            cls = document.querySelector("#uBanClsTicketsInput").value;
                            xhr = new XMLHttpRequest();
                            xhr.open("GET", "/admin/support/ban/" + {$user->getId()} + "?reason=" + res + "&close_tickets=" + cls + "&hash=" + {rawurlencode($csrfToken)}, true);
                            xhr.onload = (function() {
                                if(xhr.responseText.indexOf("success") === -1)
                                    MessageBox("Ошибка", "Не удалось заблокировать пользователя в поддержке...", ["OK"], [Function.noop]);
                                else
                                    MessageBox("Операция успешна", "Пользователь заблокирован в поддержке", ["OK"], [Function.noop]);
                            });
                            xhr.send(null);
                        }),
                        Function.noop
                    ]);
                }
            {/if}
        </script>
    </div>
    
    {/if}
    
    {else} {* isBanned() *}
        {include "banned.xml"}
    {/if}
{/block}
