{extends "../@listView.xml"}
{var $perPage = 6} {* Why 6? Check User::_abstractRelationGenerator *}

{var $act = $_GET["act"] ?? "friends"}

{if $act == "incoming"}
    {var $iterator = iterator_to_array($user->getRequests($page))}
    {var $count    = $user->getRequestsCount()}
{elseif $act == "outcoming"}
    {var $iterator = iterator_to_array($user->getSubscriptions($page))}
    {var $count    = $user->getSubscriptionsCount()}
{elseif $act == "followers"}
    {var $iterator = iterator_to_array($user->getFollowers($page))}
    {var $count    = $user->getFollowersCount()}
{elseif $act == "online"}
    {var $iterator = iterator_to_array($user->getFriendsOnline($page))}
    {var $count    = $user->getFriendsOnlineCount()}
{else}
    {var $iterator = iterator_to_array($user->getFriends($page))}
    {var $count    = $user->getFriendsCount()}
{/if}

{block title}
    {if $act == "incoming"}
        {_incoming_req}
    {elseif $act == "outcoming"}
        {_outcoming_req}
    {elseif $act == "followers"}
        {_followers}
    {elseif $act == "online"}
        {_friends_online}
    {else}
        {_friends}
    {/if}
{/block} 

{block header}
    {if isset($thisUser) && $thisUser->getId() == $user->getId()}
        {_my_friends}
    {else}
        <a href="{$user->getURL()}">{$user->getCanonicalName()}</a> »
        {if $act == "incoming"}
            {_incoming_req}
        {elseif $act == "outcoming"}
            {_outcoming_req}
        {elseif $act == "followers"}
            {_followers}
        {elseif $act == "online"}
            {_friends_online}
        {else}
            {_friends}
        {/if}
    {/if}
{/block}

{block tabs}
    <div n:attr="id => ($act === 'friends' ? 'activetabs' : 'ki')" class="tab">
        <a n:attr="id => ($act === 'friends' ? 'act_tab_a' : 'ki')" href="?">{_all_friends}</a>
    </div>
    <div n:attr="id => ($act === 'online' ? 'activetabs' : 'ki')" class="tab">
        <a n:attr="id => ($act === 'online' ? 'act_tab_a' : 'ki')" href="?act=online">{_online}</a>
    </div>
    <div n:if="!is_null($thisUser) && $user->getId() === $thisUser->getId()" n:attr="id => ($act === 'incoming' || $act === 'followers' || $act === 'outcoming' ? 'activetabs' : 'ki')" class="tab">
        <a n:attr="id => ($act === 'incoming' || $act === 'followers' || $act === 'outcoming' ? 'act_tab_a' : 'ki')" href="?act=incoming">{_req}</a>
    </div>
{/block}

{block size}
    <div n:if="$act === 'incoming' || $act === 'followers' || $act === 'outcoming'" class="mb_tabs">
        <div n:attr="id => ($act === 'incoming' ? 'active' : 'ki')" class="mb_tab">
            <div>
                <a href="?act=incoming">{_incoming_req}</a>
            </div>
        </div>
        <div n:attr="id => ($act === 'followers' ? 'active' : 'ki')" class="mb_tab">
            <div>
                <a href="?act=followers">{_followers}</a>
            </div>
        </div>
        <div n:attr="id => ($act === 'outcoming' ? 'active' : 'ki')" class="mb_tab">
            <div>
                <a href="?act=outcoming">{_outcoming_req}</a>
            </div>
        </div>
    </div>
    <div style="padding-bottom: 0px;" class="summaryBar">
        <div class="summary">
            {if !is_null($thisUser) && $user->getId() === $thisUser->getId()}
                {if $act == "incoming"}
                    {tr("req", $count)}
                {elseif $act == "outcoming"}
                    {tr("req", $count)}
                {elseif $act == "followers"}
                    {tr("followers", $count)}
                {elseif $act == "online"}
                    {tr("friends_list_online", $count)}
                {else}
                    {tr("friends_list", $count)}
                {/if}
            {else}
                {tr("friends", $count)}
            {/if}
        </div>
    </div>
{/block}

{* BEGIN ELEMENTS DESCRIPTION *}

{block link|strip|stripHtml}
    {$x->getURL()}
{/block}

{block preview}
    <img src="{$x->getAvatarUrl('miniscule')}" width="75" alt="Фотография пользователя" loading=lazy />
{/block}

{block name}
    {$x->getCanonicalName()}
    <img n:if="$x->isVerified()" 
        class="name-checkmark" 
        src="/assets/packages/static/openvk/img/checkmark.png" 
    />
{/block}

{block description}
    <table>
        <tbody>
            <tr>
                <td width="120" valign="top"><span class="nobold">{_pronouns}: </span></td>
                <td>{$x->isFemale() ? tr("female") : ($x->isNeutral() ? tr("neutral") : tr("male"))}</td>
            </tr>
            <tr>
                <td width="120" valign="top"><span class="nobold">{_relationship}:</span></td>
                <td>{$x->getLocalizedMaritalStatus()}</td>
            </tr>
            <tr>
                <td width="120" valign="top"><span class="nobold">{_registration_date}: </span></td>
                <td>{$x->getRegistrationTime()}</td>
            </tr>
        </tbody>
    </table>
{/block}

{block actions}
    {if ($x->getId() !== $thisUser->getId()) && ($thisUser->getId() === $user->getId())}
        {var $subStatus = $x->getSubscriptionStatus($thisUser)}
        {if $subStatus === 0}
            <form action="/setSub/user" method="post" class="profile_link_form" id="_submitUserSubscriptionAction">
                <input type="hidden" name="act" value="add" />
                <input type="hidden" name="id"  value="{$x->getId()}" />
                <input type="hidden" name="hash" value="{$csrfToken}" />
                <input type="submit" class="profile_link" value="{_friends_add}" />
            </form>
        {elseif $subStatus === 1}
            <form action="/setSub/user" method="post" class="profile_link_form" id="_submitUserSubscriptionAction">
                <input type="hidden" name="act" value="add" />
                <input type="hidden" name="id"  value="{$x->getId()}" />
                <input type="hidden" name="hash" value="{$csrfToken}" />
                <input type="submit" class="profile_link" value="{_friends_accept}" />
            </form>
            {if $act !== 'followers'}
                <form action="/setSub/user" method="post" class="profile_link_form" id="_submitUserSubscriptionAction">
                    <input type="hidden" name="act" value="rej" />
                    <input type="hidden" name="id"  value="{$x->getId()}" />
                    <input type="hidden" name="hash" value="{$csrfToken}" />
                    <input type="submit" class="profile_link" value="{_friends_leave_in_flw}" />
                </form>
            {/if}
        {elseif $subStatus === 2}
            <form action="/setSub/user" method="post" class="profile_link_form" id="_submitUserSubscriptionAction">
                <input type="hidden" name="act" value="rem" />
                <input type="hidden" name="id"  value="{$x->getId()}" />
                <input type="hidden" name="hash" value="{$csrfToken}" />
                <input type="submit" class="profile_link" value="{_friends_reject}" />
            </form>
        {elseif $subStatus === 3}
            <a href="/im?sel={$x->getId()}" class="profile_link" rel="nofollow">{_send_message}</a>
            <form action="/setSub/user" method="post" class="profile_link_form" id="_submitUserSubscriptionAction">
                <input type="hidden" name="act" value="rem" />
                <input type="hidden" name="id"  value="{$x->getId()}" />
                <input type="hidden" name="hash" value="{$csrfToken}" />
                <input type="submit" class="profile_link" value="{_friends_delete}" />
            </form>
        {/if}
    {/if}
{/block}