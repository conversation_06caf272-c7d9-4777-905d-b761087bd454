{extends "@layout.xml"}

{block title}
    {if $form->id === 0}
        {_admin_newgift}
    {else}
        {_gift} "{$form->name}"
    {/if}
{/block}

{block heading}
    {include title}
{/block}

{block content}
    <form class="aui" method="POST" enctype="multipart/form-data">
        <div class="field-group">
            <label for="avatar">
                {_admin_image}
                <span n:if="$form->id === 0" class="aui-icon icon-required"></span>
            </label>
            {if $form->id === 0}
                <input type="file" name="pic" accept="image/jpeg,image/png,image/gif,image/webp" required="required" />
            {else}
                <span id="avatar" class="aui-avatar aui-avatar-project aui-avatar-xlarge">
                    <span class="aui-avatar-inner">
                        <img id="pic" src="{$form->pic}" style="object-fit: cover;"></img>
                    </span>
                </span>
                <input style="display: none;" id="picInput" type="file" name="pic" accept="image/jpeg,image/png,image/gif,image/webp" />
                <div class="description">
                    <a id="picChange" href="javascript:false">{_admin_image_replace}</a>
                </div>
            {/if}
        </div>

        <div class="field-group">
            <label for="id">ID</label>
            <input class="text long-field" type="number" id="id" disabled="disabled" value="{$form->id}" />
        </div>
        <div class="field-group">
            <label for="usages">{_admin_uses}</label>
            <input class="text long-field" type="number" id="usages" disabled="disabled" value="{$form->usages}" />
            <div n:if="$form->usages > 0" class="description">
                <a href="javascript:$('#usages').value(0);">{_admin_uses_reset}</a>
            </div>
        </div>
        <div class="field-group">
            <label for="name">
                {_admin_name}
                <span class="aui-icon icon-required"></span>
            </label>
            <input class="text long-field" type="text" id="name" name="name" value="{$form->name}" />
        </div>
        <div class="field-group">
            <label for="price">
                {_admin_price}
                <span class="aui-icon icon-required"></span>
            </label>
            <input class="text long-field" type="number" id="price" name="price" min="0" value="{$form->price}" />
        </div>
        <div class="field-group">
            <label for="limit">
                {_admin_limits}
                <span class="aui-icon icon-required"></span>
            </label>
            <input class="text long-field" type="number" min="-1" id="limit" name="limit" value="{$form->limit}" />
        </div>
        <fieldset class="group">
            <legend></legend>
            <div class="checkbox" resolved="">
                <input n:attr="disabled => $form->id === 0, checked => $form->id === 0" class="checkbox" type="checkbox" name="reset_limit" id="reset_limit" />
                <span class="aui-form-glyph"></span>

                <label for="reset_limit">{_admin_limits_reset}</label>
            </div>
        </fieldset>

        <input n:if="$form->id === 0" type="hidden" name="_cat" value="{$_GET['cat'] ?? 1}" />

        <div class="buttons-container">
            <div class="buttons">
                <input type="hidden" name="hash" value="{$csrfToken}" />
                <input class="aui-button aui-button-primary submit" type="submit" value="{_save}">
            </div>
        </div>
    </form>
{/block}

{block scripts}
    <script>
        const TRANS_GIF = "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7";

        $("#picChange").click(_ => $("#picInput").click());
        $("#picInput").bind("change", e => {
            if(typeof e.target.files[0] === "undefined")
                $("#pic").prop("src", URL.createObjectURL(TRANS_GIF));

            $("#pic").prop("src", URL.createObjectURL(e.target.files[0]));
        });
    </script>
{/block}
