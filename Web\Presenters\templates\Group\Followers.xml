{extends "../@listView.xml"}
{var $Manager = openvk\Web\Models\Entities\Manager::class}
{var $iterator = $onlyShowManagers ? $managers : $followers}
{var $count    = $paginatorConf->count}
{var $page     = $paginatorConf->page}
{var $perPage  = 6}

{block title}{_followers} {$club->getCanonicalName()}{/block}

{block header}
    <a href="{$club->getURL()}">{$club->getCanonicalName()}</a>
    » {_followers}
    <a n:if="!$onlyShowManagers" href="/club{$club->getId()}/followers?onlyAdmins=1" style="float: right;">{_all_followers}</a>
    <a n:if="$onlyShowManagers" href="/club{$club->getId()}/followers" style="float: right;">{_only_administrators}</a>
{/block}

{* BEGIN ELEMENTS DESCRIPTION *}

{block tabs}
	{if $club->canBeModifiedBy($thisUser)}
		<div class="tab">
			<a href="/club{$club->getId()}/edit">
				{_main}
			</a>
		</div>
        <div class="tab">
            <a href="/club{$club->getId()}/backdrop">
                {_backdrop_short}
            </a>
        </div>
		<div id="activetabs" class="tab">
			<a id="act_tab_a" href="/club{$club->getId()}/followers">
				{_followers}
			</a>
		</div>
	{/if}
{/block}

{block link|strip|stripHtml}
    /id{$x instanceof $Manager ? $x->getUserId() : $x->getId()}
{/block}

{block preview}
    <img src="{$x instanceof $Manager ? $x->getUser()->getAvatarURL() : $x->getAvatarURL('miniscule')}" alt="{$x instanceof $Manager ? $x->getUser()->getCanonicalName() : $x->getCanonicalName()}" width=75 />
{/block}

{block name}
    {$x instanceof $Manager ? $x->getUser()->getCanonicalName() : $x->getCanonicalName()}
{/block}

{block description}
    {var $user    = $x instanceof $Manager ? $x->getUser() : $x}
    {var $manager = $x instanceof $Manager ? $x : $club->getManager($user, !$club->canBeModifiedBy($thisUser))}
    <table>
        <tbody>
            <tr>
                <td width="120" valign="top"><span class="nobold">{_pronouns}: </span></td>
                <td>{$user->isFemale() ? tr("female") : ($user->isNeutral() ? tr("neutral") : tr("male"))}</td>
            </tr>
            <tr>
                <td width="120" valign="top"><span class="nobold">{_registration_date}: </span></td>
                <td>{$user->getRegistrationTime()}</td>
            </tr>
            <tr>
                <td width="120" valign="top"><span class="nobold">{_role}: </span></td>
                <td>
                    {($club->getOwner()->getId() == $user->getId() ? !$club->isOwnerHidden() || $club->canBeModifiedBy($thisUser) : !is_null($manager)) ? tr("administrator") : tr("follower")}
                </td>
            </tr>
            <tr n:if="$manager && !empty($manager->getComment()) || $club->getOwner()->getId() === $user->getId() && !empty($club->getOwnerComment()) && (!$club->isOwnerHidden() || $club->canBeModifiedBy($thisUser))">
                <td width="120" valign="top"><span class="nobold">{_comment}: </span></td>
                <td>
                    {if $club->getOwner()->getId() === $user->getId()}
                        {$club->getOwnerComment()}
                    {else}
                        {$manager->getComment()}
                    {/if}
                </td>
            </tr>
            
        </tbody>
    </table>
{/block}

{block actions}
    {var $user    = $x instanceof $Manager ? $x->getUser() : $x}
    {var $manager = $x instanceof $Manager ? $x : $club->getManager($user, !$club->canBeModifiedBy($thisUser))}
    {if $club->canBeModifiedBy($thisUser ?? NULL)}
        <a class="profile_link" href="/club{$club->getId()}/setAdmin?user={$user->getId()}&hash={rawurlencode($csrfToken)}" n:if="$club->getOwner()->getId() !== $user->getId()">
            {if $manager}
                {_devote}
            {else}
                {_promote_to_admin}
            {/if}
        </a>
        {if $club->getOwner()->getId() != $user->getId() && $manager && $thisUser->getId() == $club->getOwner()->getId()}
            <a class="profile_link" href="javascript:changeOwner({$club->getId()}, {$user->getId()}, '{$user->getCanonicalName()}')">
                {_promote_to_owner}
            </a>
        {/if}
        {if $manager}
            <a class="profile_link" href="javascript:setClubAdminComment('{$club->getId()}', '{$manager->getUserId()}', '{rawurlencode($csrfToken)}')">
                {_set_comment}
            </a>
        {/if}
        <a class="profile_link" n:if="$club->getOwner()->getId() === $user->getId()" href="javascript:setClubAdminComment('{$club->getId()}', '{$club->getOwner()->getId()}', '{rawurlencode($csrfToken)}')">
            {_set_comment}
        </a>
        {if $manager}
            <a class="profile_link" href="/club{$club->getId()}/setAdmin?user={$user->getId()}&hidden={(int) !$manager->isHidden()}&hash={rawurlencode($csrfToken)}">
                {if $manager->isHidden()}{_hidden_yes}{else}{_hidden_no}{/if}
            </a>
        {/if}
        {if $club->getOwner()->getId() == $user->getId()}
            <a class="profile_link" href="/club{$club->getId()}/setAdmin?user={$user->getId()}&hidden={(int) !$club->isOwnerHidden()}&hash={rawurlencode($csrfToken)}">
                {if $club->isOwnerHidden()}{_hidden_yes}{else}{_hidden_no}{/if}
            </a>
        {/if}
    {/if}
{/block}
