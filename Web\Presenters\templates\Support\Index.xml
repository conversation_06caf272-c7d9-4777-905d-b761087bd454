{extends "../@layout.xml"}
{block title}{_menu_help}{/block}

{block header}
    {_menu_help}
{/block}

{block content}
    {var $isMain = $mode === 'faq'}
    {var $isNew  = $mode === 'new'}
    {var $isList = $mode === 'list'}

    {if $thisUser}
        <div class="tabs">
            <div n:attr="id => ($isMain ? 'activetabs' : 'ki')" class="tab">
                <a n:attr="id => ($isMain ? 'act_tab_a' : 'ki')" href="/support">{_support_faq}</a>
            </div>
            <div n:if="$count > 0" n:attr="id => ($isList ? 'activetabs' : 'ki')" class="tab">
                <a n:attr="id => ($isList ? 'act_tab_a' : 'ki')" href="/support?act=list">{_support_list}</a>
            </div>
            <div n:attr="id => ($isNew ? 'activetabs' : 'ki')" class="tab">
                <a n:attr="id => ($isNew ? 'act_tab_a' : 'ki')" href="/support?act=new">{_support_new}</a>
            </div>
        </div>
        <br />
        
        {if $isNew}
            {if !is_null($banReason)}
                <center>
                    <img src="/assets/packages/static/openvk/img/oof.apng" alt="{_banned_alt}" style="width: 20%;" />
                </center>
                <p>
                    {tr("banned_in_support_1", htmlentities($thisUser->getCanonicalName()))|noescape}<br/>
                    {tr("banned_in_support_2", htmlentities($banReason))|noescape}
                </p>
            {else}
                <div class="new">
                    <form action="/support" method="post" style="margin:0;">
                        <center>
                            <input type="text" name="name" style="width: 80%; resize: vertical;" placeholder="{_support_new_title}" /><br /><br />
                            <textarea name="text" style="width: 80%; resize: vertical;" placeholder="{_support_new_content}"></textarea><br /><br />
                            <input type="hidden" name="hash" value="{$csrfToken}" />
                            <input type="submit" value="{_write}" class="button" style="margin-left: 70%;" /><br /><br />
                        </center>
                    </form>
                </div>
            {/if}
        {/if}
    {/if}

    {if $isMain}
        <h4>{_support_faq}</h4><br />
        <div n:foreach="$faq as $section" class="faq">
            <div id="faqhead">{$section[0]}</div>
            <div id="faqcontent">{$section[1]|noescape}</div>
        </div>
    {/if}

    {if $isList}
        <table n:foreach="$tickets as $ticket" border="0" style="font-size: 11px; width: 610px;" class="post">
            <tbody>
                <tr>
                    <td width="54" valign="top">
                        <center>
                            <img src="/assets/packages/static/openvk/img/note_icon.png" alt="{_support_ticket}" style="margin-top: 17px;" />
                        </center>
                    </td>
                    <td width="345" valign="top">
                        <div class="post-author">
                            <a href="/support/view/{$ticket->getId()}">
                                <b>{$ticket->getName()}</b>
                            </a>
                        </div>
                        <div class="post-content" style="padding: 4px; font-size: 11px;">
                            {_status}: {$ticket->getStatus()}
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>

        {if $count < 1}
            {include "../components/nothing.xml"}
        {/if}

        <div style="padding: 8px;">
            {include "../components/paginator.xml", conf => (object) [
                "page"     => $page,
                "count"    => $count,
                "amount"   => sizeof($tickets),
                "perPage"  => OPENVK_DEFAULT_PER_PAGE,
                "atBottom" => true,
            ]}
        </div>
    {/if}
{/block}
