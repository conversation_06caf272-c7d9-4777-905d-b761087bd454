{extends "@layout.xml"}

{block title}
    {_logs}
{/block}

{block heading}
    {_logs}
{/block}

{block content}
    {var $amount = sizeof($logs)}

    <style>
        del, ins { text-decoration: none; color: #000; }
        del { background: #fdd; }
        ins { background: #dfd; }
    </style>
    <form class="aui">
    <div>
        <select class="select medium-field" type="number" id="type" name="type" placeholder="{_logs_change_type}">
            <option value="any" n:attr="selected => !$type">{_logs_anything}</option>
            <option value="0" n:attr="selected => $type === 0">{_logs_adding}</option>
            <option value="1" n:attr="selected => $type === 1">{_logs_editing}</option>
            <option value="2" n:attr="selected => $type === 2">{_logs_removing}</option>
            <option value="3" n:attr="selected => $type === 3">{_logs_restoring}</option>
        </select>
        <input class="text medium-field" type="number" id="id" name="id" placeholder="{_logs_id_post}" n:attr="value => $id"/>
        <input class="text medium-field" type="text" id="uid" name="uid" placeholder="{_logs_uuid_user}" n:attr="value => $user"/>
    </div>
    <div style="margin: 8px 0;" />
    <div>
        <select class="select medium-field" id="obj_type" name="obj_type" placeholder="{_logs_change_object}">
            <option value="any" n:attr="selected => !$obj_type">{_logs_anything}</option>
            <option n:foreach="$object_types as $type" n:attr="selected => $obj_type === $type">{$type}</option>
        </select>
        <input class="text medium-field" type="number" id="obj_id" name="obj_id" placeholder="{_logs_id_object}" n:attr="value => $obj_id"/>
        <input type="submit" class="aui-button aui-button-primary medium-field" value="Поиск" style="width: 165px;"/>
    </div>
    </form>
    <table class="aui aui-table-list">
        <thead>
            <tr>
                <th>ID</th>
                <th>{_logs_user}</th>
                <th>{_logs_object}</th>
                <th>{_logs_type}</th>
                <th>{_logs_changes}</th>
                <th>{_logs_time}</th>
            </tr>
        </thead>
        <tbody>
            <tr n:foreach="$logs as $log">
                <td>{$log->getId()}</td>
                <td>
                    <a href="/admin/chandler/user/{$log->getUser()}" target="_blank">{$log->getUser()}</a>
                </td>
                <td>
                    <span n:if="$log->getObjectAvatar()" class="aui-avatar aui-avatar-xsmall">
                        <span class="aui-avatar-inner">
                            <img src="{$log->getObjectAvatar()}" alt="{$log->getObjectName()}" style="object-fit: cover;" role="presentation" />
                        </span>
                    </span>
                    <a href="{$log->getObjectURL()}">{$log->getObjectName()}</a>
                </td>
                <td>{_$log->getTypeNom()}</td>
                <td>
                    {foreach $log->getChanges() as $change}
                        <div>
                            <b>{$change["field"]}</b>:
                            {if array_key_exists('diff', $change)}
                                {$change["diff"]|noescape}
                            {else}
                                <ins>{$change["old_value"]}</ins>
                            {/if}
                        </div>
                    {/foreach}
                </td>
                <td>
                    {=new openvk\Web\Util\DateTime($change["ts"])}
                </td>
            </tr>
        </tbody>
    </table>
    <br/>
    <div align="right">
        {var $isLast = ((20 * (($_GET['p'] ?? 1) - 1)) + $amount) < $count}

        <a n:if="($_GET['p'] ?? 1) > 1" class="aui-button" href="?p={($_GET['p'] ?? 1) - 1}">&laquo;</a>
        <a n:if="$isLast" class="aui-button" href="?p={($_GET['p'] ?? 1) + 1}">&raquo;</a>
    </div>
{/block}
