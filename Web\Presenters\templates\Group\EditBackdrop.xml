{extends "../@layout.xml"}
{var $backdrops = $club->getBackDropPictureURLs()}

{block title}{$club->getName()} | {_backdrop}{/block}

{block header}
<a href="{$club->getURL()}">{$club->getName()}</a> » {_backdrop}
{/block}

{block content}
    <div class="tabs">
        <div class="tab">
            <a href="/club{$club->getId()}/edit">
                {_main}
            </a>
        </div>
        <div id="activetabs" class="tab">
            <a id="act_tab_a" href="/club{$club->getId()}/backdrop">
                {_backdrop_short}
            </a>
        </div>
        <div class="tab">
            <a href="/club{$club->getId()}/followers">
                {_followers}
            </a>
        </div>
    </div>

    <div class="container_gray">
        <h4>{_backdrop}</h4>
        <p>{_backdrop_desc}</p>
        <form method="POST" enctype="multipart/form-data">
            <div id="backdropEditor">
                <div id="backdropFilePicker">
                    <label class="button" style="">{_browse}
                        <input type="file" accept="image/*" name="backdrop1" style="display: none;">
                    </label>
                    <div id="spacer" style="width: 366px;"></div>
                    <label class="button" style="">{_browse}<input type="file" accept="image/*" name="backdrop2" style="display: none;"></label>
                    <div id="spacer" style="width: 366px;"></div>
                </div>
            </div>

            <p>
                <span class="nobold">{_backdrop_warn}</span>
            </p>
            <p>
                <span class="nobold">{_backdrop_about_adding}</span>
            </p>
            <p><br/></p>

            <input type="hidden" name="hash" value="{$csrfToken}" />
            <div>
                <center>
                    <button name="subact" value="save" class="button">{_backdrop_save}</button>
                    <button name="subact" value="remove" class="button">{_backdrop_remove}</button>
                </center>
            </div>
        </form>
    </div>
{/block}
