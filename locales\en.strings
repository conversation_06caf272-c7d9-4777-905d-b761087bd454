"__locale" = "en_US.UTF-8;Eng";
"__transNames" = "[\P{script=Han}]; Russian-Latin/BGN; Any-Latin";

/* Check for https://www.unicode.org/cldr/charts/latest/supplemental/language_plural_rules.html */

/* Main page */

"home" = "Home";
"welcome" = "Welcome";
"to_top" = "To top";

/* Login */

"log_in" = "Log in";
"password" = "Password";
"registration" = "Registration";
"forgot_password" = "Forgot your password?";

"checkbox_in_registration" = "I agree to the <a href='/privacy'>privacy policy</a> and <a href='/terms'>site policies</a>";
"checkbox_in_registration_unchecked" = "You must agree to the privacy policy and rules in order to register.";

"login_failed" = "Login failed";
"invalid_username_or_password" = "The username or password you entered is incorrect. <a href='/restore'>Forgot your password?</a>";

"failed_to_register" = "Failed to register";
"referral_link_invalid" = "The referral link is invalid.";
"registration_disabled" = "Registration has been disabled by the system administrator.";
"user_already_exists" = "A user with this email already exists.";

"access_recovery" = "Access recovery";
"page_access_recovery" = "Restore access to the page";
"access_recovery_info" = "Forgot your password? Don't worry, enter your details and we'll send you an email with instructions on how to recover your account.";
"access_recovery_info_2" = "Enter your new password. All current sessions will be suspended and access tokens will be revoked.";
"reset_password" = "Reset the password";
"2fa_code_2" = "Two-factor authentication code";

"password_successfully_reset" = "Your password has been successfully reset.";
"password_reset_email_sent" = "If you are registered, you will receive instructions via email.";
"password_reset_error" = "An unexpected error occurred while resetting the password.";
"password_reset_rate_limit_error" = "You can't do it that often, sorry.";

"email_sent" = "Email has been successfully sent.";
"email_sent_desc" = "If this email address exists, you will receive instructions.";
"email_error" = "An unexpected error occurred while sending the email.";
"email_rate_limit_error" = "You can't do it that often, sorry.";

"email_verify_success" = "Your email has been verified. Have a great time!";

"registration_disabled_info" = "Registration has been disabled by the system administrator. If possible, request an invitation from your friend if they are registered on this site.";
"registration_closed" = "Registration is closed.";
"invites_you_to" = "<strong>$1</strong> invites you to $2";

"register_meta_desc" = "Register in $1 now!";
"register_referer_meta_title" = "$1 invites you to $2!";
"register_referer_meta_desc" = "Join $1 and many other users at $2!";
"registration_welcome_1" = "is a universal colleague search tool based on the VKontakte structure.";
"registration_welcome_2" = "We want friends, classmates, neighbors and colleagues to always be in touch.";

"users" = "Users";
"other_fields" = "Other fields";

/* Profile information */

"select_language" = "Choose language";
"edit" = "Edit";
"birth_date" = "Birth date";
"registration_date" = "Registration date";
"hometown" = "Hometown";
"this_is_you" = "that's you";
"edit_page" = "Edit page";
"edit_group" = "Edit group";
"change_status" = "change status";
"name" = "Name";
"surname" = "Surname";
"pronouns" = "Pronouns";
"male" = "he/him";
"female" = "she/her";
"neutral" = "they/them";
"description" = "Description";
"save" = "Save";
"main_information" = "Main information";
"additional_information" = "Additional information";
"nickname" = "Nickname";
"online" = "Online";
"was_online" = "was online";
"was_online_m" = "was online";
"was_online_f" = "was online";
/* For male and female */
"all_title" = "All";
"information" = "Information";
"status" = "Status";
"no_information_provided" = "No information provided.";
"deceased_person" = "Deceased person";
"none" = "none";
"desc_none" = "no description";
"send" = "Send";

"years_zero" = "0 years old";
"years_one" = "1 year old";
"years_other" = "$1 years old";

"show_my_birthday" = "Show my birthday";
"show_only_month_and_day" = "Show only month and day";

"relationship" = "Relationship";

"relationship_0" = "Not selected";
"relationship_1" = "Not married";
"relationship_2" = "Dating";
"relationship_3" = "Engaged";
"relationship_4" = "Married";
"relationship_5" = "In a civil marriage";
"relationship_6" = "In love";
"relationship_7" = "It's complicated";
"relationship_8" = "Actively searching";

/* xd */
"relationship_2_prefix" = "with";
"relationship_3_prefix" = "with";
"relationship_4_prefix" = "with";
"relationship_5_prefix" = "with";
"relationship_6_prefix" = "with";
"relationship_7_prefix" = "with";

"politViews" = "Political views";

"politViews_0" = "Not Selected";
"politViews_1" = "Indifferent";
"politViews_2" = "Communist";
"politViews_3" = "Socialist";
"politViews_4" = "Moderate";
"politViews_5" = "Liberal";
"politViews_6" = "Conservative";
"politViews_7" = "Monarchical";
"politViews_8" = "Ultra-Conservative";
"politViews_9" = "Libertarian";

"contact_information" = "Contact information";

"email" = "Email";
"phone" = "Phone";
"telegram" = "Telegram";
"personal_website" = "Personal website";
"city" = "City";
"address" = "Address";

"personal_information" = "Personal information";

"interests" = "Interests";
"additional" = "Additional";
"additional_fields_description" = "There you can add additional info about you, like another profiles links or your interests. Up to a maximum of $1 such fields can be added.";
"additional_field_name" = "Name";
"additional_field_text" = "Text";
"additional_field_place" = "Place";
"additional_field_place_contacts" = "In \"contacts\"";
"additional_field_place_interests" = "In \"interests\"";

"favorite_music" = "Favorite music";
"favorite_films" = "Favorite flims";
"favorite_shows" = "Favorite TV-shows";
"favorite_books" = "Favorite books";
"favorite_quotes" = "Favorite quotes";
"favorite_games" = "Favorite games";
"custom_field_favorite_performers" = "Favourite performers";
"custom_field_favorite_content_makers" = "Favourite content-makers";
"custom_field_favorite_anime" = "Favourite anime";
"custom_field_favorite_manga" = "Favourite manga";
"custom_field_favorite_vtubers" = "Favourite vtubers";
"custom_field_favorite_albums" = "Favourite music albums";

"information_about" = "About";

"updated_at" = "Updated at $1";

"user_banned" = "Unfortunately, we had to block <b>$1's</b> user page.";
"user_banned_comment" = "Moderator's comment:";
"verified_page" = "Verified page";
"user_is_blocked" = "User is blocked";
"before" = "before";
"forever" = "forever";

"closed_page" = "Closed page";

"limited_access_to_page_m" = "$1 limited access to his page.";
"limited_access_to_page_f" = "$1 limited access to her page.";

"you_can_add" = "You can";
"add_to_friends_m" = "add him to friends.";
"add_to_friends_f" = "add her to friends.";

"register_to_access_page_m" = "Sign up to get access to his page.";
"register_to_access_page_f" = "Sign up to get access to her page.";

/* Wall */

"feed" = "News";

"post_writes_m" = "wrote";
"post_writes_f" = "wrote";
"post_writes_g" = "published";
"post_deact_m" = "deleted his profile saying:";
"post_deact_f" = "deleted her profile saying:";
"post_deact_g" = "deleted their profile saying:";
"post_deact_silent_m" = "silently deleted his profile.";
"post_deact_silent_f" = "silently deleted her profile.";
"post_deact_silent_f" = "silently deleted their profile.";
"post_on_your_wall" = "on your wall";
"post_on_group_wall" = "in $1";
"post_on_user_wall" = "on $1's wall";
"wall" = "Wall";
"post" = "Post";
"write" = "Write";
"publish" = "Publish";
"delete" = "Delete";
"comments" = "Comments";
"share" = "Share";
"pin" = "Pin";
"unpin" = "Unpin";
"pinned" = "pinned";
"comments_tip" = "Be the first to leave a comment on this post!";
"your_comment" = "Your comment";
"auditory" = "Auditory";
"in_wall" = "to my wall";
"in_group" = "to a group";
"shown" = "Shown";
"x_out_of" = "$1 of";
"wall_zero" = "no posts";
"wall_one" = "$1 post";
"wall_other" = "$1 posts";
"publish_post" = "Add post";
"view_other_comments" = "View other comments";

"no_comments" = "No comments";

"my_news" = "My news";
"all_news" = "All news";
"posts_per_page" = "Number of posts per page";
"show_ignored_sources" = "Show ignored sources";
"auto_scroll" = "Autoscroll";
"ajax_routing" = "AJAX-transitions";

"attachment" = "Attachment";
"post_as_group" = "Post as group";
"comment_as_group" = "Comment as group";

"add_source" = "Add source";
"set_source" = "Apply source";
"source" = "Source";
"set_source_tip" = "If you are using content from other authors, it is important to provide a source to the original.<br>You can do it below.";

"add_signature" = "Add signature";
/* ^ can be translated as "author's signature". ^ */
"contains_nsfw" = "Contains NSFW content";
"nsfw_warning" = "This post may have NSFW-content";
"report" = "Report";
"attach" = "Attach";
"detach" = "Detach";
"attach_photo" = "Attach photo";
"attach_video" = "Attach video";
"attach_geotag" = "Attach geotag";
"draw_graffiti" = "Draw graffiti";
"no_posts_abstract" = "Nobody wrote anything here... So far.";
"attach_no_longer_available" = "This attachment is no longer available.";
"open_post" = "Open post";
"version_incompatibility" = "This attachment could not be displayed. Probably because the database is incompatible with the current version of OpenVK.";

"graffiti" = "Graffiti";

"reply" = "Reply";
"post_is_ad" = "This post is sponsored.";

"edited_short" = "edited";

"feed_settings" = "Feed settings";
"ignored_sources" = "Ignored sources";
"ignore_user" = "Ignore user";
"unignore_user" = "Unignore user";

"ignore_club" = "Ignore club";
"unignore_club" = "Unignore club";
"no_ignores_count" = "No ignored sources.";
"stop_ignore" = "Unignore";
"start_from_page" = "Start from page";

"all_posts" = "All posts";
"users_posts" = "Posts by $1";
"clubs_posts" = "Group's posts";
"others_posts" = "Others posts";

"show_more" = "Show more";
"has_repost" = "Has repost";

"likers_list" = "Likers list";
"liked_verb" = "Liked by";

"liked_by_x_people_one" = "Liked by $1 user";
"liked_by_x_people_few" = "Liked by $1 users";
"liked_by_x_people_many" = "Liked by $1 users";
"liked_by_x_people_other" = "Liked by $1 users";
"liked_by_x_people_zero" = "Nobody liked";

"geo_place" = "Place";
"geotag" = "Geotag";
"nearest_posts" = "Nearest posts";
"Latitude" = "Latitude";
"longitude" = "Longitude";
"name_of_the_place" = "Place's name";
"no_nearest_posts" = "No nearby posts";
"shown_last_nearest_posts" = "Showing last $1 posts per month";

"change_geo_name" = "Change geo name";
"change_geo_name_new" = "New name";

/* Friends */

"friends" = "Friends";
"followers" = "Followers";
"follower" = "Follower";
"friends_add" = "Add to friends";
"friends_delete" = "Remove from friends";
"friends_reject" = "Cancel request";
"friends_accept" = "Accept request";
"friends_leave_in_flw" = "Leave in followers";
"friends_add_msg" = "Now you're friends.";
"friends_rej_msg" = "You left this user as subscriber.";
"friends_rem_msg" = "You deleted this user from your friend list.";
"send_message" = "Send a message";
"incoming_req" = "Pending";
"outcoming_req" = "Outgoing";
"req" = "Requests";
"friends_online" = "Friends online";
"all_friends" = "All friends";

"req_zero" = "No requests were found...";
"req_one" = "Found $1 request";
"req_other" = "Found $1 requests";

"friends_zero" = "No friends";
"friends_one" = "$1 friend";
"friends_other" = "$1 friends";

"friends_online_zero" = "No friends online";
"friends_online_one" = "$1 friend is online";
"friends_online_other" = "$1 friends are online";

"friends_list_zero" = "You have no friends yet";
"friends_list_one" = "You have $1 friend";
"friends_list_other" = "You have $1 friends";

"friends_list_online_zero" = "You have no friends online";
"friends_list_online_one" = "You have $1 friend online";
"friends_list_online_other" = "You have $1 friends online";

"followers_zero" = "No followers";
"followers_one" = "$1 follower";
"followers_other" = "$1 followers";

"subscriptions_zero" = "No subscriptions";
"subscriptions_one" = "$1 subscription";
"subscriptions_other" = "$1 subscriptions";

/* Group */

"group" = "Group";
"name_group" = "Name";
"subscribe" = "Subscribe";
"unsubscribe" = "Unsubscribe";
"subscriptions" = "Subscriptions";
"join_community" = "Join community";
"leave_community" = "Leave community";
"check_community" = "View community";
"min_6_community" = "Name of the group must have more that 6 characters";
"participants" = "Participants";
"groups" = "Groups";
"meetings" = "Meetings";
"create_group" = "Create group";
"group_managers" = "Managers";
"group_type" = "Group type";
"group_type_open" = "This is an open group. Anyone can enter it.";
"group_type_closed" = "This is a closed group. To enter, you must submit a request.";
"creator" = "Creator";
"administrators" = "Administrators";
"add_to_left_menu" = "Add to left menu";
"remove_from_left_menu" = "Remove from left menu";
"all_followers" = "All followers";
"only_administrators" = "Only administrators";
"website" = "Website";
"managed" = "Managed";
"size" = "Size";

"administrators_one" = "$1 administrator";
"administrators_other" = "$1 administrators";

"role" = "Role";
"administrator" = "Administrator";
"promote_to_admin" = "Promote to admin";
"promote_to_owner" = "Promote to owner";
"devote" = "Devote";
"set_comment" = "Set comment";
"hidden_yes" = "Hidden: Yes";
"hidden_no" = "Hidden: No";
"group_allow_post_for_everyone" = "Open";
"group_limited_post" = "Suggestions";
"group_closed_post" = "Closed";
"suggest_new" = "Suggest post";

"suggested_by_you_zero" = "$1 suggested posts by you";
"suggested_by_you_one" = "One suggested post by you";
"suggested_by_you_few" = "$1 suggested posts by you";
"suggested_by_you_many" = "$1 suggested posts by you";
"suggested_by_you_other" = "$1 suggested posts by you";

"suggested_by_everyone_zero" = "$1 suggested posts";
"suggested_by_everyone_one" = "One suggested post";
"suggested_by_everyone_few" = "$1 suggested posts";
"suggested_by_everyone_many" = "$1 suggested posts";
"suggested_by_everyone_other" = "$1 suggested posts";

"group_hide_from_global_feed" = "Don't display posts in global feed";
"suggested_posts_by_you" = "Your suggested posts";
"suggested_posts_by_everyone" = "Suggested posts";
"suggested" = "Suggested";
"suggested_posts_everyone" = "Posts suggested by users";
"no_suggested_posts_by_you" = "You haven't suggested any post to this group yet.";
"no_suggested_posts_by_people" = "No posts have been suggested to this group yet.";

"publish_suggested" = "Accept";
"decline_suggested" = "Decline";

"error_loading_suggest" = "Error when loading new posts";

"publishing_suggested_post" = "Publishing suggested post";
"suggested_posts_in_group_zero" = "You've looked at all the suggested posts, congratulations!";
"suggested_posts_in_group_one" = "This group has one suggested post";
"suggested_posts_in_group_few" = "This group has $1 suggested posts";
"suggested_posts_in_group_many" = "This group has $1 suggested posts";
"suggested_posts_in_group_other" = "This group has $1 suggested posts";

"suggested_posts_in_group_by_you_zero" = "You haven't suggested any post to this group";
"suggested_posts_in_group_by_you_one" = "You suggested one post to this group";
"suggested_posts_in_group_by_you_few" = "You suggested $1 posts to this group";
"suggested_posts_in_group_by_you_many" = "You suggested $1 posts to this group";
"suggested_posts_in_group_by_you_other" = "You suggested $1 posts to this group";

"suggestion_successfully_published" = "Post successfully published";
"suggestion_successfully_declined" = "Post successfully declined";
"suggestion_press_to_go" = "Go to post";

"error_declining_invalid_post" = "The suggested post you attempted to decline is invalid";
"error_declining_not_suggested_post" = "The post you attempted to decline is not suggested";
"error_declining_declined_post" = "This suggested post has already been declined";

"error_accepting_invalid_post" = "The suggested post you attempted to accept is invalid";
"error_accepting_not_suggested_post" = "The post you attempted to accept is not suggested";
"error_accepting_declined_post" = "This suggested post has already been declined";

"statistics" = "Statistics";
"group_administrators_list" = "Admins list";
"group_display_only_creator" = "Display only group creator";
"group_display_all_administrators" = "Display all administrators";
"group_dont_display_administrators_list" = "Display nothing";

"group_changeowner_modal_title" = "Transfer Owner's Permissions";
"group_changeowner_modal_text" = "Attention! You are transferring ownership rights to user $1. This action is irreversible. After the transfer, you will remain an administrator, but you can easily step down from this role.";
"group_owner_setted" = "The new owner ($1) has been successfully assigned to the community $2. You have been granted administrator rights in the community. If you wish to revert to the owner role, please contact <a href='/support?act=new'>site technical support</a>.";

"participants_zero" = "No participants";
"participants_one" = "$1 participant";
"participants_other" = "$1 participant";

"groups_zero" = "No groups";
"groups_one" = "$1 group";
"groups_other" = "$1 groups";

"groups_list_zero" = "You are not a participant in any group";
"groups_list_one" = "You are participating in one group";
"groups_list_other" = "You are a participant of $1 groups";

"meetings_zero" = "No meetings";
"meetings_one" = "$1 meeting";
"meetings_other" = "$1 meetings";

"search_by_groups" = "Explore Groups";

"error_suggestions" = "Error accessing to suggestions";
"error_suggestions_closed" = "This group has closed wall.";
"error_suggestions_open" = "This group has open wall.";
"error_suggestions_access" = "Only group's administrators can view all suggested posts.";

"group_banned" = "Unfortunately, we had to block the <b>$1</b> group.";

/* Albums */

"create" = "Create";
"albums" = "Albums";
"album" = "Album";
"photos" = "photos";
"photo" = "Photo";
"create_album" = "Create album";
"edit_album" = "Edit album";
"edit_photo" = "Edit photo";
"creating_album" = "Creating album";
"delete_photo" = "Delete photo";
"sure_deleting_photo" = "Do you sure you want to delete this photo from album?";
"upload_photo" = "Upload photo";
"photo" = "Photo";
"upload_button" = "Upload";

"open_original" = "Open original";

"avatar_album" = "Profile photos";
"wall_album" = "Wall photos";

"albums_zero" = "No albums";
"albums_one" = "$1 album";
"albums_other" = "$1 albums";

"albums_list_zero" = "You don't have any albums";
"albums_list_one" = "You have one album";
"albums_list_other" = "You have $1 albums";

"add_image" = "Add image";
"add_image_group" = "Upload image";
"upload_new_picture" = "Upload new photo";
"uploading_new_image" = "Uploading new photo";
"friends_avatar" = "It will be easier for friends to recognize you if you upload your real picture.";
"groups_avatar" = "Good photo can make your group more recognizable.";
"formats_avatar" = "You can upload an image in JPG, GIF or PNG format.";
"troubles_avatar" = "If you're having trouble uploading, try selecting a smaller photo.";
"webcam_avatar" = "If your computer has a webcam, you can <a id='_takeSelfie'>take a snapshot »</a>";
"publish_on_wall" = "Make post on wall";
"take_snapshot" = "Take snapshot";
"your_browser_doesnt_support_webcam" = "Your browser does not support webcam video capture.";

"selected_area_user" = "The selected area will be shown on your page.";
"selected_area_club" = "The selected area will be shown on the group page.";
"selected_area_rotate" = "If the image is not oriented correctly, the photo can be rotated.";

"deleting_avatar" = "Deleting photo";
"deleting_avatar_sure" = "Do you sure you want to delete avatar?";

"deleted_avatar_notification" = "Picture deleted successfully";
"save_changes" = "Save changes";

"upd_m" = "updated his profile picture";
"upd_f" = "updated her profile picture";
"upd_n" = "updated their profile picture";
"upd_g" = "updated group's picture";

"add_photos" = "Add photos";
"upload_picts" = "Upload photos";
"end_uploading" = "Finish uploading";
"photos_successfully_uploaded" = "Photos uploaded successfully";
"click_to_go_to_album" = "Click here to go to album.";
"error_uploading_photo" = "Error when uploading photo";
"too_many_pictures" = "No more than 10 pictures";
"too_many_attachments" = "Too many attachments.";

"drag_files_here" = "Drag files here";
"only_images_accepted" = "File \"$1\" is not an image";
"max_filesize" = "Max filesize is $1 MB";

"uploading_photos_from_computer" = "Uploading photos from Your computer";
"supported_formats" = "Supported file formats: JPG, PNG, and GIF.";
"max_load_photos" = "You can upload up to 10 photos at a time.";
"tip" = "Tip";
"tip_ctrl" = "To select multiple photos at once, hold down the Ctrl key when selecting files in Windows or the CMD key in Mac OS.";
"album_poster" = "Album Poster";
"select_photo" = "Select Photos";
"upload_new_photo" = "Upload New Photo";

"is_x_photos_zero" = "Just zero photos.";
"is_x_photos_one" = "Just one photo.";
"is_x_photos_few" = "Just $1 photos.";
"is_x_photos_many" = "Just $1 photos.";
"is_x_photos_other" = "Just $1 photos.";

"all_photos" = "All photos";
"error_uploading_photo" = "Error when uploading photo. Error text: ";
"too_many_photos" = "Too many photos.";

"photo_x_from_y" = "Photo $1 from $2";

/* Notes */

"notes" = "Notes";
"note" = "Note";
"name_note" = "Title";
"text_note" = "Content";
"create_note" = "Add note";
"edit_note" = "Edit note";
"actions" = "Actions";

"edited" = "Edited";

"notes_zero" = "No notes";
"notes_one" = "$1 note";
"notes_other" = "$1 notes";
"notes_start_screen" = "Use notes to share your events with friends and stay updated on theirs.";
"note_preview" = "Preview Draft";
"note_preview_warn" = "Preview Mode Activated";
"note_preview_warn_details" = "Note behavior or appearance may change after saving. Avoid excessive use of preview.";
"note_preview_empty_err" = "Previewing an empty or nameless note? Why, Mr. White?";

"notes_list_zero" = "No notes found";
"notes_list_one" = "$1 note found";
"notes_list_other" = "$1 notes found";

"select_note" = "Selecting note";
"no_notes" = "You don't have any notes";

"error_attaching_note" = "Error when attaching note";

"select_or_create_new" = "Select an existing note or <a href='/notes/create'>create a new one</a>";

"notes_closed" = "You can't attach a note to the post because only you can see them.<br> You can change this in <a href=\"/settings?act=privacy\">settings</a>.";
"do_not_attach_note" = "Do not attach a note";
"something_is_supported_from_xhtml" = "<a href='/kb/notes'>Something</a> from (X)HTML supported.";
"something" = "Something";
"supports_xhtml" = "from (X)HTML supported.";

/* Notes: Article Viewer */
"aw_legacy_ui" = "Legacy interface";

/* Menus */

/* Note that is string need to fit into the "My Page" link */

"edit_button" = "edit";
"my_page" = "My Page";
"my_friends" = "My Friends";
"my_photos" = "My Photos";
"my_videos" = "My Videos";
"my_messages" = "My Messages";
"my_notes" = "My Notes";
"my_audios" = "My Audios";
"my_groups" = "My Groups";
"my_feed" = "My Feed";
"my_feedback" = "My Feedback";
"my_settings" = "My Settings";
"bookmarks" = "Bookmarks";
"bookmarks_tab" = "Saved";
"bug_tracker" = "Bug Tracker";

"menu_settings" = "Settings";
"menu_login" = "Login";
"menu_registration" = "Register";

"menu_help" = "Help";

"menu_logout" = "Log out";
"menu_support" = "Support";

"header_home" = "home";
"header_groups" = "groups";
"header_people" = "people";
"header_invite" = "invite";
"header_help" = "help";
"header_log_out" = "log out";
"header_search" = "Search";

"header_login" = "login";
"header_registration" = "registration";

"left_menu_donate" = "Donate";

"footer_about_instance" = "about instance";
"footer_rules" = "rules";
"footer_blog" = "blog";
"footer_help" = "help";
"footer_developers" = "developers";
"footer_choose_language" = "choose language";
"footer_privacy" = "privacy";

/* Settings */

"main" = "Main";
"contacts" = "Contacts";
"avatar" = "Avatar";
"privacy" = "Privacy";
"interface" = "Interface";
"security" = "Security";

"profile_picture" = "Profile picture";

"picture" = "Picture";

"change_password" = "Change password";
"old_password" = "Old password";
"new_password" = "New password";
"repeat_password" = "Repeat password";

"avatars_style" = "Avatar style";
"style" = "Style";

"default" = "default";

"arbitrary_avatars" = "Arbitrary";
"cut" = "Square";
"round_avatars" = "Round";

"apply_style_for_this_device" = "Apply style only for this device";
"ui_settings_window" = "Advanced settings";

"search_for_groups" = "Search for groups";
"search_for_users" = "Search for users";
"search_for_posts" = "Search for posts";
"search_for_comments" = "Search for comments";
"search_for_videos" = "Search for videos";
"search_for_apps" = "Search for apps";
"search_for_notes" = "Search for notes";
"search_for_audios" = "Search for music";
"search_for_audios_playlists" = "Search for playlists";
"search_for_docs" = "Search for documents";
"search_button" = "Find";
"search_placeholder" = "Start typing any name, title or word";
"results_zero" = "No results";
"results_one" = "$1 result";
"results_other" = "$1 results";

"privacy_setting_access_page" = "Who can view my page";
"privacy_setting_read_info" = "Who can see the main information on my page";
"privacy_setting_see_groups" = "Who can see my groups and meetings";
"privacy_setting_see_photos" = "Who can see my photos";
"privacy_setting_see_videos" = "Who can see my videos";
"privacy_setting_see_notes" = "Who can see my notes";
"privacy_setting_see_friends" = "Who can see my friends";
"privacy_setting_add_to_friends" = "Who can add me to friends";
"privacy_setting_write_wall" = "Who can publish posts on my wall";
"privacy_setting_write_messages" = "Who can write messages to me";
"privacy_setting_view_audio" = "Who can see my audios";
"privacy_setting_see_likes" = "Who can see my likes";
"privacy_value_anybody" = "Anybody";
"privacy_value_anybody_dative" = "Anybody";
"privacy_value_users" = "OpenVK users";
"privacy_value_friends" = "Friends";
"privacy_value_friends_dative" = "Friends";
"privacy_value_only_me" = "Only me";
"privacy_value_only_me_dative" = "Only me";
"privacy_value_nobody" = "Nobody";

"profile_type" = "Profile type";
"profile_type_open" = "Open";
"profile_type_closed" = "Closed";

"your_email_address" = "Your Email address";
"your_page_address" = "Your address page";
"page_address" = "Address page";
"current_email_address" = "Current email address";
"new_email_address" = "New email address";
"save_email_address" = "Save email address";
"page_id" = "Page ID";
"you_can_also" = "You can also";
"delete_your_page" = "delete your page";
"delete_album" = "delete album";

"ui_settings_interface" = "Interface";
"ui_settings_sidebar" = "Left menu";
"ui_settings_rating" = "Rating";
"ui_settings_rating_show" = "Show";
"ui_settings_rating_hide" = "Hide";
"ui_settings_nsfw_content" = "NSFW content";
"ui_settings_nsfw_content_dont_show" = "Don't show in global feed";
"ui_settings_nsfw_content_blur" = "Just blur";
"ui_settings_nsfw_content_show" = "Show";
"ui_settings_view_of_posts" = "View of posts";
"ui_settings_view_of_posts_old" = "Old";
"ui_settings_view_of_posts_microblog" = "Microblog";
"ui_settings_main_page" = "Main page";
"ui_settings_sessions" = "Sessions";

"additional_links" = "Additional links";
"ad_poster" = "Ad poster";

"email_change_confirm_message" = "Please confirm your new email address for the change to take effect. We have sent instructions to it.";

"profile_deactivate" = "Delete Account";
"profile_deactivate_button" = "Delete Account";
"profile_deactivate_header" = "We're sorry to see you go. Please share the reason for deleting your account and any feedback you have. Your input helps us improve the site!";
"profile_deactivate_reason_header" = "Please select a reason for leaving";
"profile_deactivate_reason_1" = "I have another profile and don't need this one";
"profile_deactivate_reason_1_text" = "I created a new page and now I want to wipe my past.";
"profile_deactivate_reason_2" = "The website consumes too much of my time";
"profile_deactivate_reason_2_text" = "While the site is nice, it's taking away time I need for work and life.";
"profile_deactivate_reason_3" = "The website has inappropriate content";
"profile_deactivate_reason_3_text" = "I have found enough porn and pirated content to last me a lifetime, I'm leaving now.";
"profile_deactivate_reason_4" = "I am concerned about the safety of my data";
"profile_deactivate_reason_4_text" = "I feel uneasy about my privacy here. I have to leave.";
"profile_deactivate_reason_5" = "No one comments on my posts";
"profile_deactivate_reason_5_text" = "This lack of engagement is disheartening. You'll regret losing me.";
"profile_deactivate_reason_6" = "Other reason";

"profile_deactivated_msg" = "Your account has been <b>deleted</b>.<br/><br/>If you would like to start using the site again, you can <a href='/settings/reactivate'>restore your account</a> until $1.";
"profile_deactivated_status" = "Account deleted";
"profile_deactivated_info" = "This account has been deleted.<br/>No further information available.";

"share_with_friends" = "Share with friends";

"end_all_sessions" = "End all sessions";
"end_all_sessions_description" = "If you wish to log out of $1 on all devices, click the button below.";

"end_all_sessions_done" = "All sessions have been terminated, including mobile apps.";

"backdrop_short" = "Backdrop";
"backdrop" = "Page Backdrop";
"backdrop_desc" = "You can set two pictures as the backdrop for your profile or group. They will be displayed on the left and right edges of the page, adding personality to your profile.";
"backdrop_warn" = "The images will be arranged as shown in the layout above. Their height will be automatically increased to occupy 100% of the screen height, with a blur in the middle. You cannot replace the background of the main OpenVK interface or add audio.";
"backdrop_about_adding" = "You can upload only one picture, although depending on the design, the end result may look imperfect. You can also change only one picture: if you already have two images set up and you want to change one, upload only one, and the other won't be removed. To remove both images, press the button below; you can't remove pictures individually.";
"backdrop_save" = "Save Backdrop Picture(s)";
"backdrop_remove" = "Remove All Backdrop Pictures";
"backdrop_error_title" = "Error Saving Backdrop Settings";
"backdrop_error_no_media" = "Images are corrupted or haven't been uploaded in their entirety.";
"backdrop_succ" = "Backdrop settings saved successfully";
"backdrop_succ_rem" = "Backdrop images have been successfully removed";
"backdrop_succ_desc" = "Users will start seeing changes in 5 minutes.";
"browse" = "Browse";

/* Two-factor authentication */

"two_factor_authentication" = "Two-Factor Authentication";
"two_factor_authentication_disabled" = "Enhances security by requiring a code from the 2FA app to access your account, providing reliable protection against hacking.";
"two_factor_authentication_enabled" = "Two-Factor Authentication is active. Your account is securely protected.";
"two_factor_authentication_login" = "Two-Factor Authentication is enabled. Enter the code received in the application to log in.";

"two_factor_authentication_settings_1" = "Two-Factor Authentication via TOTP can be used even without an internet connection. You'll need a code generation app such as <b>Google Authenticator</b> for Android and iOS or <b>FOSS Aegis and andOTP</b> for Android. Ensure your phone's date and time are set correctly.";
"two_factor_authentication_settings_2" = "Using the app for two-factor authentication, scan the QR code below:";
"two_factor_authentication_settings_3" = "Alternatively, manually enter the provided secret key: <b>$1</b>.";
"two_factor_authentication_settings_4" = "Now, enter the code generated by the app and your account password to confirm your identity.";

"connect" = "Connect";
"enable" = "Enable";
"disable" = "Disable";
"code" = "Code";
"2fa_code" = "2FA code";

"incorrect_password" = "Password is incorrect";
"incorrect_code" = "Code is incorrect";
"incorrect_2fa_code" = "Two-factor authentication code is incorrect";
"two_factor_authentication_enabled_message" = "Two-factor authentication is now enabled";
"two_factor_authentication_enabled_message_description" = "Your account is now more secure. We recommend downloading <a href='javascript:viewBackupCodes()'>backup codes</a>.";
"two_factor_authentication_disabled_message" = "Two-factor authentication has been disabled";

"view_backup_codes" = "See backup codes";
"backup_codes" = "Backup codes for login verification";
"two_factor_authentication_backup_codes_1" = "Backup codes enable you to confirm your login when you can't access your phone, such as when traveling.";
"two_factor_authentication_backup_codes_2" = "You have <b>10 more codes</b>, and each code can only be used once. Print them, store them securely, and use when needed for login verification.";
"two_factor_authentication_backup_codes_3" = "You can request new codes if needed. Only the latest set of backup codes generated is valid.";
"viewing_backup_codes" = "View backup codes";
"disable_2fa" = "Turn off 2FA";
"viewing" = "View";

/* OAuth */
"identifies_itself_as" = "that identifies itself as $1";
"located_at_url" = "located at $1";
"wants_your_token" = "wants to access your account";
"app_will_have_access_to" = "App will have access to:";
"oauth_scope_all" = "profile information, status, list of friends, photos, wall posts, audios, videos, notifications, fishing rod handle, messages, gifts, <b>your e-mail</b>, polls, communities, discussions, notes, <b>payment method</b>, likes and comments";
"oauth_grant" = "Allow";
"oauth_deny" = "Deny";

/* Sorting */

"sort_randomly" = "Sort randomly";
"sort_up" = "Sort by ID up";
"sort_down" = "Sort by ID down";

"new_first" = "New frist";
"old_first" = "Old first";

/* Videos */

"videos" = "Videos";
"video" = "Video";
"upload_video" = "Upload video";
"video_uploaded" = "Uploaded";
"video_updated" = "Updated";
"video_link_to_yt" = "Link to YouTube";

"info_name" = "Title";
"info_description" = "Description";
"info_uploaded_by" = "Uploaded by";
"info_upload_date" = "Upload date";

"videos_zero" = "No videos";
"videos_one" = "$1 video";
"videos_other" = "$1 videos";

"view_video" = "View";

"selecting_video" = "Selecting videos";
"upload_new_video" = "Upload new video";
"max_attached_videos" = "Max is 10 videos";
"max_attached_photos" = "Max is 10 photos";
"max_attached_audios" = "Max is 10 audios";
"no_videos" = "You don't have uploaded videos.";
"no_videos_results" = "No results.";

"video_file_upload" = "Upload file";
"video_youtube_upload" = "Add from YouTube";

"change_video" = "Change video";
"unknown_video" = "This video is not supported in your version of OpenVK.";


/* Audios */

"my" = "My";
"audios" = "Audios";
"audio" = "Audio";
"playlist" = "Playlist";
"upload_audio" = "Upload audio";
"upload_audio_to_group" = "Upload audio to group";

"performer" = "Performer";
"audio_name" = "Name";
"genre" = "Genre";
"lyrics" = "Lyrics";

"select_another_file" = "Upload another file";

"limits" = "Limits";
"select_audio" = "Select audio from your computer";
"audio_requirements" = "Audio must be between $1 seconds and $2 minutes, with a file size up to $3 MB, and contain an audio stream.";
"audio_requirements_2" = "Audio must not infringe copyright and related rights.";
"you_can_also_add_audio_using" = "You can also add audio from files you have already downloaded using";
"search_audio_inst" = "audios search";

"audio_embed_not_found" = "Audio not found";
"audio_embed_deleted" = "Audio has been deleted";
"audio_embed_withdrawn" = "The audio has been withdrawn at the request of the copyright holder";
"audio_embed_forbidden" = "The user's privacy settings do not allow this audio to be embedded";
"audio_embed_processing" = "Audio is processing.";
"audio_embed_processing_bait" = "Play anyway";

"audios_count_zero" = "No audios";
"audios_count_one" = "One audio";
"audios_count_few" = "$1 audios";
"audios_count_many" = "$1 audios";
"audios_count_other" = "$1 audios";

"track_unknown" = "Unknown";
"track_noname" = "Untitled";

"my_music" = "My music";
"music_user" = "User's music";
"music_club" = "Group's music";
"audio_new" = "New";
"audio_popular" = "Popular";
"audio_search" = "Search";

"my_audios_small" = "My audios";
"my_audios_small_uploaded" = "Uploaded";
"my_playlists" = "My playlists";
"playlists" = "Playlists";
"audios_explicit" = "Contains obscene language";
"audios_unlisted" = "Unlisted";
"withdrawn" = "Withdrawn";
"deleted" = "Deleted";
"owner" = "Owner";
"searchable" = "Searchable";

"select_audio" = "Select audios";
"no_playlists_thisuser" = "You haven't added any playlists yet.";
"no_playlists_user" = "This user has not added any playlists yet.";
"no_playlists_club" = "This group hasn't added playlists yet.";

"no_audios_thisuser" = "You haven't added any audios yet.";
"no_audios_user" = "This user has not added any audios yet.";
"no_audios_club" = "This group has not added any audios yet.";

"new_playlist" = "New playlist";
"created_playlist" = "created";
"updated_playlist" = "updated";
"bookmark" = "Add to collection";
"unbookmark" = "Remove from collection";
"empty_playlist" = "There are no audios in this playlist.";
"edit_playlist" = "Edit playlist";
"unable_to_load_queue" = "Error when loading queue.";

"fully_delete_audio" = "Delete audio";
"attach_audio" = "Attach audio";
"detach_audio" = "Detach audio";

"show_more_audios" = "Show more audios";
"add_to_playlist" = "Add to playlist";
"remove_from_playlist" = "Remove from playlist";
"delete_playlist" = "Delete playlist";

"playlists_user" = "User's playlists";
"playlists_club" = "Group's playlists";
"change_cover" = "Change cover";
"playlist_cover" = "Playlist cover";

"minutes_count_zero" = "Lasts no minutes";
"minutes_count_one" = "Lasts one minute";
"minutes_count_few" = "Lasts $1 minutes";
"minutes_count_many" = "Lasts $1 minutes";
"minutes_count_other" = "Lasts $1 minutes";

"listens_count_zero" = "No listens";
"listens_count_one" = "One listen";
"listens_count_few" = "$1 listens";
"listens_count_many" = "$1 listens";
"listens_count_other" = "$1 listens";

"add_audio_to_club" = "Add audio to group";
"add_audio" = "Adding audio";
"add_audio_limitations" = "You can select up to 10 groups/playlists.";
"to_club" = "To club";
"to_playlist" = "To playlist";

"audio_was_successfully_added" = "Audios was successfully added.";
"what_club_add" = "Which group do you want to add the song to?";
"group_has_audio" = "This group already has this song.";
"group_hasnt_audio" = "This group doesn't have this song.";

"by_name" = "by name";
"by_performer" = "by performer";
"no_access_clubs" = "There are no groups where you are an administrator.";
"no_access_playlists" = "You haven't created any playlists.";
"audio_successfully_uploaded" = "Audio has been successfully uploaded and is currently being processed.";

"broadcast_audio" = "Broadcast audio to status";
"sure_delete_playlist" = "Are you sure you want to delete this playlist?";
"edit_audio" = "Edit audio";
"audios_group" = "Audios from group";
"playlists_group" = "Playlists from group";

"play_tip" = "Play/pause";
"repeat_tip" = "Repeat";
"shuffle_tip" = "Shuffle";
"mute_tip" = "Mute";
"mute_tip_noun" = "Muted";
"playlist_hide_from_search" = "Unlisted";
"confirm_deleting_audio" = "Do you sure want to delete this audio?";

"copy_link_to_audio" = "Copy link to clipboard";
"copy_link_to_audio_error_not_selected_track" = "Track was not selected";
"audio_ctx_add_to_group" = "Add to group";
"audio_ctx_add_to_playlist" = "Add to playlist";
"audio_ctx_play_next" = "Play next";
"audio_ctx_clear_context" = "Clear tracks list";

"is_x_audio_zero" = "No audios";
"is_x_audio_one" = "Just one audio.";
"is_x_audio_few" = "Just $1 audios.";
"is_x_audio_many" = "Just $1 audios.";
"is_x_audio_other" = "Just $1 audios.";

/* Notifications */

"feedback" = "Feedback";
"unread" = "Unread";
"archive" = "Archive";

"notifications_like" = "$1 liked your $2post$3 from $4";
"notifications_repost" = "$1 shared your $2post$3 from $4";
"notifications_comment_under" = "$1 left a comment on $2";
"notifications_under_note" = "your $3note$4";
"notifications_under_photo" = "your $3photo$4";
"notifications_under_post" = "your $3post$4 from $5";
"notifications_under_video" = "your $3video$4";
"notifications_post" = "$1 published $2a post$3 on your wall: $4";
"notifications_appoint" = "$1 appointed you as community manager of $2";

"nt_liked_yours" = "liked your";
"nt_shared_yours" = "shared your";
"nt_commented_yours" = "commented";
"nt_written_on_your_wall" = "wrote on your wall";
"nt_accepted_your_post" = "accepted your suggested";
"nt_in_club" = "In group";
"nt_new_suggested_posts" = "new posts in suggestions";
"nt_made_you_admin" = "appointed you in the community";

"nt_from" = "from";
"nt_yours_adjective" = "your";
"nt_yours_feminitive_adjective" = "your";
"nt_post_nominative" = "post";
"nt_post_instrumental" = "post";
"nt_note_instrumental" = "note";
"nt_photo_instrumental" = "photo";
"nt_topic_instrumental" = "topic";

"nt_you_were_mentioned_u" = "You were mentioned by user";
"nt_you_were_mentioned_g" = "You were mentioned by group";
"nt_mention_in_post_or_comms" = "in post or one of its discussion threads";
"nt_mention_in_photo" = "in discussion of this photo";
"nt_mention_in_video" = "in discussion of this video";
"nt_mention_in_note" = "in discussion of this note";
"nt_mention_in_topic" = "in the discussion";
"nt_sent_gift" = "sent you a gift";

"nt_post_small" = "post";

/* Time */

"time_at_sp" = " at ";
"time_just_now" = "just now";
"time_exactly_five_minutes_ago" = "5 minutes ago";
"time_minutes_ago" = "$1 minutes ago";
"time_today" = "today";
"time_yesterday" = "yesterday";

"news" = "News";
"news_more" = "More »";
"points" = "Votes";
"points_count" = "votes";
"on_your_account" = "on your account";
"top_up_your_account" = "Get more";
"you_still_have_x_points" = "You have <b>$1</b> unused votes.";

"vouchers" = "Vouchers";
"have_voucher" = "Have a voucher";
"voucher_token" = "Voucher token";
"voucher_activators" = "Users";
"voucher_explanation" = "Enter the voucher serial number, usually found on the receipt or in the message.";
"voucher_explanation_ex" = "Note that vouchers can expire and are valid for a single use only.";
"invalid_voucher" = "Invalid voucher";
"voucher_bad" = "You may have entered the wrong serial number, used a voucher already, or the voucher has expired.";
"voucher_good" = "Voucher activated";
"voucher_redeemed" = "The voucher has been successfully activated. You will receive points, but this code can no longer be used.";
"redeem" = "Redeem voucher";
"deactivate" = "Deactivate";
"usages_total" = "Total uses";
"usages_left" = "Uses remaining";

"points_transfer_dialog_header_1" = "You can send as a gift or transfer part of the votes to another person.";
"points_transfer_dialog_header_2" = "Your current balance:";

"points_amount_one" = "1 vote";
"points_amount_other" = "$1 votes";

"transfer_poins" = "Transfer votes";
"transfer_poins_button" = "Transfer votes";
"also_you_can_transfer_points" = "You can also <a href=\"javascript:showCoinsTransferDialog($1, '$2')\">transfer votes</a> to another person.";

"transferred_to_you" = "transferred to you";

"transfer_trough_ton" = "Top up with TON";
"transfer_ton_contents" = "You can top up your balance with TON cryptocurrency. It's just enough to scan the QR-code with the Tonkeeper app, or manually send TON according to the requisites. Within a few minutes you will receive a certain amount of votes.";
"transfer_ton_address" = "<b>Wallet address:</b> $1<br/><b>Message content:</b> $2";
"transfer_ton_currency_per_ton" = "$1 TON";

"receiver_address" = "Receiver address";
"coins_count" = "Number of votes";
"message" = "Message";

"failed_to_transfer_points" = "Failed to transfer votes";

"points_transfer_successful" = "You have successfully transferred <b>$1</b> to <b><a href=\"$2\">$3</a></b>.";
"not_all_information_has_been_entered" = "Not all information has been entered.";
"negative_transfer_value" = "We cannot steal votes from another person, sorry.";
"message_is_too_long" = "The message is too long.";
"receiver_not_found" = "The receiver was not found.";
"you_dont_have_enough_points" = "You don't have enough votes.";

"increase_rating" = "Increase rating";
"increase_rating_button" = "Increase";
"to_whom" = "To whom";
"increase_by" = "Increase by";
"price" = "Price";

"you_have_unused_votes" = "You have $1 unused votes on your balance.";
"apply_voucher" = "Apply voucher";

"failed_to_increase_rating" = "Failed to increase rating";
"rating_increase_successful" = "You have successfully increased the rating of <b><a href=\"$1\">$2</a></b> by <b>$3%</b>.";
"negative_rating_value" = "You can't steal ratings from another person.";

"increased_your_rating_by" = "increased your rating by";

/* Gifts */

"gift" = "Gift";
"gifts" = "Gifts";
"gifts_zero" = "Zero gifts";
"gifts_one" = "One gift";
"gifts_other" = "$1 gifts";
"gifts_left" = "Gifts left: $1";
"gifts_left_zero" = "Zero gifts left";
"gifts_left_one" = "One gift left";
"gifts_left_other" = "$1 gifts left";

"send_gift" = "Send gift";

"gift_select" = "Select gift";
"collections" = "Collections";
"confirm" = "Confirm";
"as_anonymous" = "As anonymous";
"gift_your_message" = "Your message";

"free_gift" = "Free";
"coins" = "Votes";
"coins_zero" = "0 vote";
"coins_one" = "One vote";
"coins_other" = "$1 votes";

"users_gifts" = "Gifts";
"sent" = "Sent";

/* Apps */
"app" = "Application";
"apps" = "Applications";
"my_apps" = "My Apps";
"all_apps" = "All apps";
"installed_apps" = "Installed apps";
"own_apps" = "Own apps";
"own_apps_alternate" = "My apps";

"app_play" = "start";
"app_uninstall" = "uninstall";
"app_edit" = "edit";
"app_dev" = "Developer";

"create_app" = "Create an application";
"edit_app" = "Edit an application";
"new_app" = "New application";
"app_news" = "A news note";
"app_state" = "Status";
"app_enabled" = "Enabled";
"app_creation_hint_url" = "Specify the exact address in the URL, including the scheme (https), port (80), and required request parameters.";
"app_creation_hint_iframe" = "Your application will open in an iframe.";
"app_balance" = "Your application has <b>$1</b> votes credited to it.";
"app_users" = "Your application is being used by <b>$1</b> people.";
"app_withdrawal_q" = "Do you want to withdraw?";
"app_withdrawal" = "Withdraw";
"app_withdrawal_empty" = "Sorry, withdrawal of emptiness is not possible.";
"app_withdrawal_created" = "A request to withdraw $1 votes has been created. Awaiting crediting.";

"appjs_payment" = "Purchase Payment";
"appjs_payment_intro" = "You are about to pay for an order in the application";
"appjs_order_items" = "Order items";
"appjs_payment_total" = "Total amount payable";
"appjs_payment_confirm" = "Pay";
"appjs_err_funds" = "Failed to pay: insufficient funds.";

"appjs_wall_post" = "Publish a Post";
"appjs_wall_post_desc" = "wants to publish a post on your wall";

"appjs_act_friends" = "Your Friends";
"appjs_act_friends_desc" = "add users as friends and read your friends list";
"appjs_act_wall" = "Your Wall";
"appjs_act_wall_desc" = "see your news, your wall and create posts on it";
"appjs_act_messages" = "Your Messages";
"appjs_act_messages_desc" = "read and write messages on your behalf";
"appjs_act_groups" = "Your Groups";
"appjs_act_groups_desc" = "see a list of your groups and subscribe you to others";
"appjs_act_likes" = "Likes Feature";
"appjs_act_likes_desc" = "give and take away likes to posts";

"appjs_act_request" = "Access Request";
"appjs_act_requests" = "requests access to";
"appjs_act_can" = "The app will be able to";
"appjs_act_allow" = "Allow";
"appjs_act_disallow" = "Disallow";

"app_uninstalled" = "Application Disabled";
"app_uninstalled_desc" = "It will no longer be able to perform actions on your behalf.";
"app_err_not_found" = "Application Not Found";
"app_err_not_found_desc" = "Incorrect identifier or it has been disabled.";
"app_err_forbidden_desc" = "This application does not belong to you.";
"app_err_url" = "Incorrect Address";
"app_err_url_desc" = "The address of the application did not pass the check; make sure it is correct.";
"app_err_ava" = "Unable to Upload an Avatar";
"app_err_ava_desc" = "Avatar is too big or incorrect: general error #$res.";
"app_err_note" = "Failed to Attach a News Note";
"app_err_note_desc" = "Make sure the link is correct and the note belongs to you.";

"learn_more" = "Learn more";

/* Support */

"support_opened" = "Opened";
"support_answered" = "Has a response";
"support_closed" = "Closed";
"support_ticket" = "Ticket";
"support_tickets" = "Tickets";
"support_status_0" = "Issue under consideration";
"support_status_1" = "There's a response";
"support_status_2" = "Closed";
"support_greeting_hi" = "Greetings, $1!";
"support_greeting_regards" = "Best regards,<br/>$1 Support Team.";

"support_faq" = "Frequently Asked Questions";
"support_list" = "List of Tickets";
"support_new" = "New Ticket";

"support_new_title" = "Enter the topic of your ticket";
"support_new_content" = "Describe the issue or suggestion";

"reports" = "Reports";

"support_rate_good_answer" = "This is a good answer";
"support_rate_bad_answer" = "This is a bad answer";
"support_good_answer_user" = "You left positive feedback.";
"support_bad_answer_user" = "You left negative feedback.";
"support_good_answer_agent" = "User left positive feedback.";
"support_bad_answer_agent" = "User left negative feedback.";
"support_rated_good" = "You left positive feedback about the answer.";
"support_rated_bad" = "You left negative feedback about the answer.";
"wrong_parameters" = "Invalid request parameters.";

"fast_answers" = "Quick Answers";

"ignore_report" = "Ignore report";
"report_number" = "Report #";
"list_of_reports" = "List of reports";
"text_of_the_post" = "Text of the post";
"delete_content" = "Delete content";
"today" = "today";

"will_be_watched" = "It will be reviewed by the moderators soon";

"report_question" = "Report?";
"report_question_text" = "What exactly do you find unacceptable about this material?";
"report_reason" = "Report reason";
"reason" = "Reason";
"going_to_report_app" = "You are about to report this application.";
"going_to_report_club" = "You are about to report this club.";
"going_to_report_photo" = "You are about to report this photo.";
"going_to_report_user" = "You are about to report this user.";
"going_to_report_video" = "You are about to report this video.";
"going_to_report_audio" = "You are about to report this audio.";
"going_to_report_doc" = "You are about to report this document.";
"going_to_report_post" = "You are about to report this post.";
"going_to_report_comment" = "You are about to report this comment.";

"comment" = "Comment";
"sender" = "Sender";

"author" = "Author";

"ticket_changed" = "Ticket Updated";
"ticket_changed_comment" = "Changes will be applied in a few seconds.";

"banned_in_support_1" = "Apologies, <b>$1</b>, but you're currently banned from creating tickets.";
"banned_in_support_2" = "The reason is straightforward: <b>$1</b>. Unfortunately, this privilege has been permanently revoked this time.";

"you_can_close_this_ticket_1" = "If you have no more questions, you can ";
"you_can_close_this_ticket_2" = "close this ticket";
"agent_profile_created_1" = "Profile created";
"agent_profile_created_2" = "Now users see your customized name and avatar instead of the default ones.";
"agent_profile_edited" = "Profile edited";
"agent_profile" = "My Agent Card";

/* Invite */

"invite" = "Invite";
"you_can_invite" = "You can invite your friends or acquaintances to the network using an individual link:";
"you_can_invite_2" = "Attach this link to your post. When the user signs up, he will immediately appear in your friends.";

/* Banned */

"banned_title" = "You are banned";
"banned_header" = "You are banned";
"banned_alt" = "The user is blocked.";
"banned_1" = "Apologies, <b>$1</b>, but you have been banned.";
"banned_2" = "The reason for this action is simple: <b>$1</b>.";
"banned_perm" = "Regrettably, this time the ban is permanent.";
"banned_until_time" = "This time, the ban extends until <b>$1</b>";
"banned_3" = "You can still <a href=\"/support?act=new\">reach out to support</a> if you believe there was an error or <a href=\"/logout?hash=$1\">logout</a>.";
"banned_unban_myself" = "Unban myself";
"banned_unban_title" = "Your account has been unbanned";
"banned_unban_description" = "Kindly refrain from violating the rules in the future.";

/* Registration confirm */

"ec_header" = "Confirm Registration";
"ec_title" = "Thank You!";
"ec_1" = "<b>$1</b>, your registration is nearly complete. In a few minutes, you should receive an email with a link to verify your email address.";
"ec_2" = "If, for any reason, you don't receive the email, please check your spam folder. If it's not there, you can request a resend.";
"ec_resend" = "Resend Email";

/* Messages */

"all_messages" = "All messages";

"search_messages" = "Search messages";
"no_messages" = "No one has written to you yet.";
"messages_blocked" = "Unfortunately, your message cannot be sent due to this user's privacy settings.";
"enter_message" = "Enter message";

"messages_error_1" = "Message not delivered";
"messages_error_1_description" = "There was a general error in sending this message...";

/* Polls */
"poll" = "Poll";
"create_poll" = "Create poll";
"poll_title" = "Ask a question";
"poll_add_option" = "Add an option...";
"poll_anonymous" = "Anonymous votes";
"poll_multiple" = "Multiple answers";
"poll_locked" = "Quiz mode (no retraction)";
"poll_edit_expires" = "Expires in: ";
"poll_edit_expires_days" = "days";
"poll_editor_tips" = "Pressing backspace in empty option will remove it. Use Tab/Enter (in last option) to create new options faster.";
"poll_embed" = "Embed code";

"poll_voter_count_zero" = "Be <b>the first</b> to vote!";
"poll_voter_count_one" = "<b>Only one</b> user voted.";
"poll_voter_count_few" = "<b>$1</b> users voted.";
"poll_voter_count_many" = "<b>$1</b> users voted.";
"poll_voter_count_other" = "<b>$1</b> users voted.";

"poll_voters_list" = "Voters";

"poll_anon" = "Anonymous";
"poll_public" = "Public";
"poll_multi" = "multiple choice";
"poll_lock" = "can't revoke";
"poll_until" = "until $1";

"poll_err_to_much_options" = "Too much options supplied.";
"poll_err_anonymous" = "Can't access voters list: poll is anonymous.";
"cast_vote" = "Vote!";
"retract_vote" = "Cancel my vote";

/* Discussions */

"discussions" = "Discussions";

"messages_one" = "One message";
"messages_other" = "$1 messages";

"topic_messages_count_zero" = "Topic has no messages";
"topic_messages_count_one" = "There is one message in the topic";
"topic_messages_count_other" = "There are $1 messages in the topic";

"replied" = "replied";
"create_topic" = "Create a topic";

"new_topic" = "New topic";
"title" = "Title";
"text" = "Text";

"view_topic" = "View topic";
"edit_topic_action" = "Edit topic";
"edit_topic" = "Edit topic";
"topic_settings" = "Topic settings";
"pin_topic" = "Pin topic";
"close_topic" = "Close topic";
"delete_topic" = "Delete topic";

"topics_one" = "One topic";
"topics_other" = "$1 topics";

"created" = "Created";

"everyone_can_create_topics" = "Everyone can create topics";
"everyone_can_upload_audios" = "Everyone can upload audios";
"display_list_of_topics_above_wall" = "Display a list of topics above the wall";

"topic_changes_saved_comment" = "The updated title and settings will appear on the topic page.";

"failed_to_create_topic" = "Failed to create topic";
"failed_to_change_topic" = "Failed to change topic";
"no_title_specified" = "No title specified.";

/* Errors */

"error_1" = "Incorrect query";
"error_2" = "Incorrect login and password";
"error_3" = "Non authorized";
"error_4" = "User does not exist";
"information_-1" = "Success";
"information_-2" = "Login success";

"no_data" = "No data";
"no_data_description" = "There is nothing here... yet.";

"error" = "Error";
"error_shorturl" = "This short address is already owned.";
"error_segmentation" = "Segmentation error";
"error_upload_failed" = "Failed to upload a photo";
"error_old_password" = "Old password does not match";
"error_new_password" = "New password does not match";
"error_weak_password" = "Your password isn't strong enough. It should be at least 8 characters long, include at least one capital letter, and contain at least one digit.";
"error_shorturl_incorrect" = "The short address has an incorrect format.";
"error_repost_fail" = "Failed to share post";
"error_data_too_big" = "Attribute '$1' must be at most $2 $3 long";

"forbidden" = "Access error";
"unknown_error" = "Unknown error";
"forbidden_comment" = "This user's privacy settings do not allow you to look at his page.";

"changes_saved" = "Changes saved";
"changes_saved_comment" = "New data will appear on your page";

"photo_saved" = "Photo saved";
"photo_saved_comment" = "New profile picture will appear on your page";

"shared_succ" = "The post will appear on your wall. Click on this notification to go there.";

"invalid_email_address" = "Invalid Email address";
"invalid_email_address_comment" = "The Email you entered is not correct.";

"invalid_real_name" = "Kindly input your actual name. It will make it simpler for your friends to find you in this way.";

"invalid_birth_date" = "Invalid date of birth";
"invalid_birth_date_comment" = "The date of birth you entered is not correct.";

"invalid_telegram_name" = "Invalid Telegram account name";
"invalid_telegram_name_comment" = "The Telegram account name you entered is not correct.";

"token_manipulation_error" = "Token manipulation error";
"token_manipulation_error_comment" = "The token is invalid or expired";

"profile_changed" = "Profile changed";
"profile_changed_comment" = "Your active profile has been changed.";
"profile_not_found" = "User was not found.";
"profile_not_found_text" = "Either this profile has been deleted, or it hasn't been created yet.";

"suspicious_registration_attempt" = "Suspicious registration attempt";
"suspicious_registration_attempt_comment" = "You tried to register from a suspicious location.";

"rate_limit_error" = "Hey, slow down!";
"rate_limit_error_comment" = "Daniel Myslivets dispises you. In $1, you cannot send requests as often. Exception code: $2.";

"not_enough_permissions" = "Not enough permissions";
"not_enough_permissions_comment" = "You do not have sufficient permissions to perform this action.";

"login_required_error" = "Not enough permissions";
"login_required_error_comment" = "To view this page, you need to sign in to your account.";

"captcha_error" = "Incorrect characters entered";
"captcha_error_comment" = "Please make sure you fill in the captcha field correctly.";

"failed_to_publish_post" = "Failed to publish post";
"failed_to_delete_post" = "Failed to delete post";

"media_file_corrupted" = "The media content file is corrupted.";
"media_file_corrupted_or_too_large" = "The media content file is corrupted or too large.";
"post_is_empty_or_too_big" = "The post is empty or too big.";
"post_is_too_big" = "The post is too big.";
"error_deleting_suggested" = "You can't delete your accepted post";
"error_invalid_wall_value" = "Invalid wall value";

"error_sending_report" = "Failed to make a report...";

"error_when_saving_gift" = "Error when saving gift";
"error_when_saving_gift_bad_image" = "Gift image is crooked.";
"error_when_saving_gift_no_image" = "Please, upload gift's image.";
"video_uploads_disabled" = "Video uploads are disabled by the system administrator.";

"error_when_publishing_comment" = "Error when publishing comment";
"error_when_publishing_comment_description" = "Image is corrupted, too big or one side is many times larger than the other.";
"error_comment_empty" = "Comment is empty or too big.";
"error_comment_too_big" = "Comment is too big.";
"error_comment_file_too_big" = "Media file is corrupted or too big.";

"comment_is_added" = "Comment has been added";
"comment_is_added_desc" = "Your comment will appear on the page.";

"error_access_denied_short" = "Access denied";
"error_access_denied" = "You don't have permission to edit this resource";
"success" = "Success";
"comment_will_not_appear" = "This comment will no longer appear.";

"error_when_gifting" = "Failed to gift";
"error_user_not_exists" = "User or pack does not exist.";
"error_no_rights_gifts" = "Failed to check rights on gift.";
"error_no_more_gifts" = "You no longer have such gifts.";
"error_no_money" = "Shout out to a beggar.";

"gift_sent" = "Gift sent";
"gift_sent_desc" = "You sent a gift to $1 for $2 votes";

"error_on_server_side" = "An error occurred on the server side. Contact your system administrator.";
"error_no_group_name" = "You did not enter a group name.";

"success_action" = "Action successful";
"connection_error" = "Connection error";
"connection_error_desc" = "Failed to connect to telemetry service.";

"error_when_uploading_photo" = "Failed to save photo";

"new_changes_desc" = "New data will appear in your group.";
"comment_is_changed" = "Admin comment changed";
"comment_is_deleted" = "Admin comment deleted";
"comment_is_too_long" = "Comment is too long ($1 characters instead of 36)";
"x_no_more_admin" = "$1 no longer an administrator.";
"x_is_admin" = "$1 appointed as an administrator.";

"x_is_now_hidden" = "Now $1 will be shown as a normal subscriber to everyone except other admins";
"x_is_now_showed" = "Now everyone will know that $1 is an administrator.";

"note_is_deleted" = "Note was deleted";
"note_x_is_now_deleted" = "Note \"$1\" was successfully deleted.";
"new_data_accepted" = "New data accepted.";

"album_is_deleted" = "Album was deleted";
"album_x_is_deleted" = "Album $1 was successfully deleted.";

"error_adding_to_deleted" = "Failed to save photo to <b>DELETED</b>.";
"error_adding_to_x" = "Failed to save photo to <b>$1</b>.";
"no_photo" = "No photo";

"select_file" = "Select file";
"new_description_will_appear" = "The updated description will appear on the photo page.";
"photo_is_deleted" = "Photo was deleted";
"photo_is_deleted_desc" = "This photo has been successfully deleted.";

"no_video" = "No video";
"no_video_desc" = "Select a file or provide a link.";
"error_occured" = "Error occured";
"error_video_damaged_file" = "The file is corrupted or does not contain video.";
"error_video_incorrect_link" = "Perhaps the link is incorrect.";
"error_video_no_title" = "Video can't be published without a title.";

"new_data_video" = "The updated description will appear on the video page.";
"error_deleting_video" = "Failed to delete video";
"login_please" = "You are not signed in.";
"invalid_code" = "Failed to verify phone number: Invalid code.";

"error_max_pinned_clubs" = "Maximum count of the pinned groups is 10.";
"error_viewing_subs" = "You cannot view the full list of subscriptions $1.";
"error_status_too_long" = "Status is too long ($1 characters instead of 255)";
"death" = "Death...";
"nehay" = "Let 'em live!";
"user_successfully_banned" = "User was successfully banned.";

"content_is_deleted" = "The content has been removed and the user has received a warning.";
"report_is_ignored" = "Report was ignored.";
"group_owner_is_banned" = "Group's owner was successfully banned";
"group_is_banned" = "Group was successfully banned";

"description_too_long" = "Description is too long.";
"invalid_club" = "This group does not exists.";
"invalid_user" = "This user does not exists.";
"ignored_sources_limit" = "Limit of ignored sources has exceed";

"invalid_audio" = "Invalid audio.";
"do_not_have_audio" = "You don't have this audio.";
"do_have_audio" = "You already have this audio.";

"set_playlist_name" = "Enter the playlist name.";
"playlist_already_bookmarked" = "This playlist is already in your collection.";
"playlist_not_bookmarked" = "This playlist is not in your collection.";
"invalid_cover_photo" = "Error when loading cover photo.";
"not_a_photo" = "Uploaded file doesn't look like a photo.";
"file_too_big" = "File is too big.";
"file_loaded_partially" = "The file has been uploaded partially.";
"file_not_uploaded" = "Failed to upload the file.";
"error_code" = "Error code: $1.";
"ffmpeg_timeout" = "Timed out waiting ffmpeg. Try to upload file again.";
"ffmpeg_not_installed" = "Failed to proccess the file. It looks like ffmpeg is not installed on this server.";
"too_many_or_to_lack" = "Too few or too many sources.";

"error_adding_source_regex" = "Error adding source: incorrect link.";
"error_adding_source_long" = "Error adding source: link is too long.";
"error_adding_source_sus" = "Error adding source: suspicious link.";
"error_playlist_creating_too_small" = "Add at least one audio";
"error_geolocation" = "Error while trying to pin geolocation";
"error_no_geotag" = "There is no geo-tag pinned in this post";

"limit_exceed_exception" = "You're doing this action too often. Try again later.";

/* Admin actions */

"login_as" = "Login as $1";
"manage_user_action" = "Manage user";
"manage_group_action" = "Manage group";
"ban_user_action" = "Ban user";
"blocks" = "Blocks";
"last_actions" = "Last actions";
"unban_user_action" = "Unban user";
"warn_user_action" = "Warn user";
"ban_in_support_user_action" = "Ban in support";
"unban_in_support_user_action" = "Unban in support";
"changes_history" = "Editing history";

/* Admin panel */

"admin" = "Admin panel";

"sandbox_for_developers" = "Sandbox for developers";
"admin_ownerid" = "Owner ID";
"admin_author" = "Author";
"admin_name" = "Name";
"admin_title" = "Title";
"admin_description" = "Description";
"admin_first_known_ip" = "First known IP";
"admin_shortcode" = "Short code";
"admin_verification" = "Verification";
"admin_hide_global_feed" = "Hide global feed";
"admin_banreason" = "Ban reason";
"admin_banned" = "banned";
"admin_gender" = "Sex";
"admin_registrationdate" = "Registration date";
"admin_actions" = "Actions";
"admin_image" = "Image";
"admin_image_replace" = "Replace the image?";
"admin_uses" = "Uses";
"admin_uses_reset" = "Reset the number of uses?";
"admin_limits" = "Limits";
"admin_limits_reset" = "Reset the number of limits";
"admin_open" = "Open";
"admin_loginas" = "Login as...";
"admin_commonsettings" = "Common settings";
"admin_langsettings" = "Language-dependent settings";

"admin_tab_main" = "General";
"admin_tab_ban" = "Ban";
"admin_tab_followers" = "Followers";

"admin_overview" = "Overview";
"admin_overview_summary" = "Summary";

"admin_content" = "User-generated content";
"admin_user_search" = "Search for users";
"admin_user_online" = "Online status";
"admin_user_online_default" = "Default";
"admin_user_online_incognito" = "Incognito";
"admin_user_online_deceased" = "Deceased";
"admin_club_search" = "Search for groups";
"admin_club_excludeglobalfeed" = "Do not display posts in the global feed";
"admin_club_enforceexcludeglobalfeed" = "Disallow group staff from changing global feed status";

"admin_services" = "Paid services";
"admin_newgift" = "New gift";
"admin_price" = "Price";
"admin_giftset" = "Gift set";
"admin_giftsets" = "Gift sets";
"admin_giftsets_none" = "There are no gift sets. Create a set to create a gift.";
"admin_giftsets_create" = "Create a gift set";
"admin_giftsets_title" = "The internal name of the set, which will be used if no name can be found in the user's language.";
"admin_giftsets_description" = "The internal description of the set, which will be used if no name can be found in the user's language.";
"admin_price_free" = "free";
"admin_voucher_rating" = "Rating";
"admin_voucher_serial" = "Serial number";
"admin_voucher_serial_desc" = "The number consists of 24 characters. If the format is incorrect or the field is not filled in, it will be assigned automatically.";
"admin_voucher_coins" = "Number of votes";
"admin_voucher_rating_number" = "Number of rating";
"admin_voucher_usages_desc" = "The number of users that can use the voucher. If you type -1, it will be infinity.";
"admin_voucher_status" = "Status";
"admin_voucher_status_opened" = "active";
"admin_voucher_status_closed" = "closed";

"admin_settings" = "Settings";
"admin_settings_tuning" = "General";
"admin_settings_appearance" = "Appearance";
"admin_settings_security" = "Security";
"admin_settings_integrations" = "Integrations";
"admin_settings_system" = "System";

"admin_about" = "About OpenVK";
"admin_about_version" = "Version";
"admin_about_instance" = "Instance";

"admin_commerce_disabled" = "Commerce has been disabled by the system administrator";
"admin_commerce_disabled_desc" = "The voucher and gift settings will be saved, but will have no effect.";

"admin_privacy_warning" = "Be careful with this information";
"admin_longpool_broken" = "Longpool is broken and will not work!";
"admin_longpool_broken_desc" = "Make sure file at the path <code>$1</code> exist and have correct rights and ownership.";

"admin_banned_links" = "Blocked links";
"admin_banned_link" = "Link";
"admin_banned_domain" = "Domain";
"admin_banned_link_description" = "With the protocol (https://example.com/)";
"admin_banned_link_regexp" = "Regular expression";
"admin_banned_link_regexp_description" = "It is substituted after the domain specified above. Don't fill it out if you want to block the entire domain";
"admin_banned_link_reason" = "Reason";
"admin_banned_link_initiator" = "Initiator";
"admin_banned_link_not_specified" = "The link is not specified";
"admin_banned_link_not_found" = "Link not found";

"admin_gift_moved_successfully" = "Gift moved successfully";
"admin_gift_moved_to_recycle" = "This gift was moved to <b>Recycle Bin</b>.";
"admin_original_file" = "Original file";
"admin_audio_length" = "Length";
"admin_cover_id" = "Cover (photo ID)";
"admin_music" = "Music";

"logs" = "Logs";
"logs_anything" = "Anything";
"logs_adding" = "Creation";
"logs_editing" = "Editing";
"logs_removing" = "Deletion";
"logs_restoring" = "Restoring";
"logs_added" = "created";
"logs_edited" = "edited";
"logs_removed" = "removed";
"logs_restored" = "restored";
"logs_id_post" = "Post ID";
"logs_id_object" = "Object ID";
"logs_uuid_user" = "User UUID";
"logs_change_type" = "Change Type";
"logs_change_object" = "Object Type";

"logs_user" = "User";
"logs_object" = "Object";
"logs_type" = "Type";
"logs_changes" = "Changes";
"logs_time" = "Time";

"bans_history" = "Blocks history";
"bans_history_blocked" = "Blocked";
"bans_history_initiator" = "Initiator";
"bans_history_start" = "Start";
"bans_history_end" = "End";
"bans_history_time" = "Time";
"bans_history_reason" = "Reason";
"bans_history_start" = "Start";
"bans_history_removed" = "Removed";
"bans_history_active" = "Active block";

/* Paginator (deprecated) */

"paginator_back" = "Back";
"paginator_page" = "Page $1";
"paginator_next" = "Next";

/* About */

"about_openvk" = "About OpenVK";

"about_this_instance" = "About this instance";
"rules" = "Rules";
"most_popular_groups" = "Most popular groups";
"on_this_instance_are" = "On this instance are:";
"about_links" = "Links";
"instance_links" = "Instance links:";

"about_users_one" = "<b>1</b> user";
"about_users_other" = "<b>$1</b> users";

"about_online_users_one" = "<b>1</b> online user";
"about_online_users_other" = "<b>$1</b> online users";

"about_active_users_one" = "<b>1</b> active user";
"about_active_users_other" = "<b>$1</b> active users";

"about_groups_one" = "<b>1</b> group";
"about_groups_other" = "<b>$1</b> groups";

"about_wall_posts_one" = "<b>1</b> wall post";
"about_wall_posts_other" = "<b>$1</b> wall posts";

"about_watch_rules" = "See <a href='$1'>here</a>.";

/* Dialogs */

"ok" = "OK";
"yes" = "Yes";
"no" = "No";
"cancel" = "Cancel";
"edit_action" = "Change";
"transfer" = "Transfer";
"close" = "Close";

"warning" = "Warning";
"question_confirm" = "This action can't be undone. Do you really wanna do it?";
"confirm_m" = "Confirm";
"action_successfully" = "Success";
"exit_noun" = "Exit";
"exit_confirmation" = "Are you sure want to exit?";
"apply" = "Apply";

/* User Alerts */

"user_alert_scam" = "This account has received numerous reports for scams. Please exercise caution, especially if they request money.";
"user_may_not_reply" = "This user may be unable to reply to you due to your privacy settings. <a href='/settings?act=privacy'>Open privacy settings</a>";

/* Cookies Pop-up */

"cookies_popup_content" = "Just like how kids love cookies, this website uses Cookies to identify your session and nothing more. Check <a href='/privacy'>our privacy policy</a> for more information.";
"cookies_popup_agree" = "Accept";

/* Blacklist */

"blacklist" = "Blacklist";
"user_blacklisted_you" = "This user has blacklisted you.";
"user_blacklisted" = "$1 has been blacklisted";
"user_removed_from_the_blacklist" = "$1 has been removed from the blacklist.";

"adding_to_bl_sure" = "You sure you want to blacklist $1?";

"bl_count_zero_desc" = "There are no users on your blacklist yet.";
"bl_count_zero" = "There are no users on your blacklist";
"bl_count_one" = "You have one user on your blacklist";
"bl_count_few" = "You have $1 users on your blacklist";
"bl_count_many" = "You have $1 users on your blacklist";
"bl_count_other" = "You have $1 users on your blacklist";

"you_blacklisted" = "You blacklisted $1";
"bl_add" = "Add to blacklist";
"bl_remove" = "Remove from blacklist";

"addition_to_bl" = "Addition to blacklist";

/* Away */

"transition_is_blocked" = "Transition is blocked";
"caution" = "Caution";
"url_is_banned" = "This link is banned";
"url_is_banned_comment" = "The <b>$1</b> administration advises against following this link.";
"url_is_banned_comment_r" = "The <b>$1</b> administration advises against following this link.<br><br>Reason: <b>$2</b>";
"url_is_banned_default_reason" = "The link you are attempting to open may lead to a site designed to deceive users for profit.";
"url_is_banned_title" = "Link to a suspicious site";
"url_is_banned_proceed" = "Proceed to the link";

"recently" = "Recently";

/* Helpdesk */
"helpdesk" = "Support";
"helpdesk_agent" = "Support Agent";
"helpdesk_agent_card" = "Agent Card";
"helpdesk_positive_answers" = "positive answers";
"helpdesk_negative_answers" = "negative answers";
"helpdesk_all_answers" = "all answers";
"helpdesk_showing_name" = "Display name";
"helpdesk_show_number" = "Show the number";
"helpdesk_avatar_url" = "Link to the avatar";

/* Chandler */
"c_user_removed_from_group" = "The user has been removed from the group";
"c_permission_removed_from_group" = "The permission has been removed from the group";
"c_group_removed" = "The group has been deleted.";
"c_groups" = "Chandler Groups";
"c_users" = "Chandler Users";
"c_group_permissions" = "Group Permissions";
"c_group_members" = "Group Members";
"c_model" = "Model";
"c_permission" = "Permission";
"c_permissions" = "Permissions";
"c_color" = "Color";
"add" = "Add";
"c_edit_groups" = "Edit Groups";
"c_user_is_not_in_group" = "No relationship found between the user and the group.";
"c_permission_not_found" = "No relationship found between the permission and the group.";
"c_group_not_found" = "Group not found.";
"c_user_is_already_in_group" = "This user is already a member of this group.";
"c_add_to_group" = "Add to group";
"c_remove_from_group" = "Remove from group";

/* Maintenance */

"global_maintenance" = "Undergoing maintenance";
"section_maintenance" = "The section is not available";
"undergoing_global_maintenance" = "Unfortunately, the instance is now closed for technical work. We are already working on troubleshooting. Please come back later.";
"undergoing_section_maintenance" = "Unfortunately, the <b>$1</b> section is temporarily unavailable. We are already working on troubleshooting. Please come back later.";

"topics" = "Topics";


/* Tutorial */

"tour_title" = "Site Tour";
"reg_title" = "Registration";
"ifnotlike_title" = " &quot;And what if I don't like this site?&quot; ";
"tour_promo" = "About what awaits you after registration";

"reg_text" = "<a href='/reg'>Registering</a> an account is absolutely free and takes no more than two minutes";
"ifnotlike_text" = "You can always delete your account";


"tour_next" = "Next →";
"tour_reg" = "Registration →";


"tour_section_1" = "Start";
"tour_section_2" = "Profile";
"tour_section_3" = "Photos";
"tour_section_4" = "Search";
"tour_section_5" = "Videos";
"tour_section_6" = "Audios";
"tour_section_7" = "Local news feed";
"tour_section_8" = "Global news feed";
"tour_section_9" = "Groups";
"tour_section_10" = "Events";
"tour_section_11" = "Themes";
"tour_section_12" = "Customization";
"tour_section_13" = "Promocodes";
"tour_section_14" = "Mobile version";


"tour_section_1_title_1" = "Where to start?";
"tour_section_1_text_1" = "Registering an account is the very first and most basic step in beginning your journey on this site.";
"tour_section_1_text_2" = "To register you will need to enter your name, E-mail and password.";
"tour_section_1_text_3" = "<b>Remember:</b> Your E-mail will be used as your login to the site. You also have the full right not to specify your last name when signing up. In case you lost your password to enter the site, use the <a href='/restore'>recovery page</a>";
"tour_section_1_bottom_text_1" = "By registering on the site, you agree to <a href='/terms'>site rules</a> and <a href='/privacy'>privacy policy</a>";


"tour_section_2_title_1" = "Your Profile";
"tour_section_2_text_1_1" = "Upon registration, you'll be redirected to <b>your</b> profile automatically.";
"tour_section_2_text_1_2" = "You have the flexibility to edit it at any time and from anywhere.";
"tour_section_2_text_1_3" = "<b>Hint:</b> To enhance your profile's appearance, consider filling it with information or uploading a photo that reflects your inner world.";
"tour_section_2_bottom_text_1" = "You decide how much information you want to share with your friends.";
"tour_section_2_title_2" = "Set Your Privacy Settings";
"tour_section_2_text_2_1" = "Define who can access specific types of information and sections on your page.";
"tour_section_2_text_2_2" = "You can block access to your page from search engines and unregistered users.";
"tour_section_2_text_2_3" = "<b>Remember:</b> Privacy settings will be expanded in the future.";
"tour_section_2_title_3" = "Profile URL";
"tour_section_2_text_3_1" = "After registering, your page is assigned a personal ID like <b>@id12345</b>.";
"tour_section_2_text_3_2" = "The <b>default ID</b> obtained at registration <b>cannot be changed</b>.";
"tour_section_2_text_3_3" = "However, in your page settings, you can link a personal address that <b>can be changed</b> at any time.";
"tour_section_2_text_3_4" = "<b>Hint:</b> Choose an address that's at least 5 characters long for a cool URL :)";
"tour_section_2_bottom_text_2" = "<i>Supports any short address in lowercase Latin letters; the address may contain numbers (not at the beginning), periods, and underscores (not at the beginning or end)</i>";
"tour_section_2_title_4" = "Wall";


"tour_section_3_title_1" = "Share Your Photo Moments";
"tour_section_3_text_1" = "The \"Photos\" section becomes available in your profile as soon as you sign up.";
"tour_section_3_text_2" = "Explore user photo albums and create your own.";
"tour_section_3_text_3" = "Control access to all your photo albums for other users in the page privacy settings.";
"tour_section_3_bottom_text_1" = "Create an unlimited number of photo albums for your travels, events, or just to store memes.";


"tour_section_4_title_1" = "Search";
"tour_section_4_text_1" = "The \"Search\" section allows you to search for users and groups.";
"tour_section_4_text_2" = "This section of the site will continue to improve over time.";
"tour_section_4_text_3" = "To start a search, you need to know the user's first (or last) name. For groups, you need to know the group's name.";
"tour_section_4_title_2" = "Quick Search";
"tour_section_4_text_4" = "To save time, use the search bar in the site header.";


"tour_section_5_title_1" = "Upload and Share Videos with Your Friends!";
"tour_section_5_text_1" = "You can upload an unlimited number of videos and clips.";
"tour_section_5_text_2" = "Control the \"Videos\" section through privacy settings.";
"tour_section_5_bottom_text_1" = "Upload videos directly to the wall, bypassing the \"Videos\" section:";
"tour_section_5_title_2" = "Importing Videos from YouTube";
"tour_section_5_text_3" = "In addition to direct uploads, the site also supports embedding videos from YouTube.";


"tour_section_6_title_1" = "Listen to music";
"tour_section_6_text_1" = "You can listen to music in \"My Audios\"";
"tour_section_6_text_2" = "This section is also controlled by the privacy settings.";
"tour_section_6_text_3" = "The most listened songs are in \"Popular\", and recently uploaded songs are in \"New\"";
"tour_section_6_text_4" = "To add a song to your collection, hover over it and click on the \"plus\". You can search for the song you want.";
"tour_section_6_text_5" = "If you can't find the song you want, you can upload it yourself";
"tour_section_6_bottom_text_1" = "<b>Important:</b> the song must not infringe copyright";
"tour_section_6_title_2" = "Create playlists";
"tour_section_6_text_6" = "You can create playlists in the \"My Playlists\" tab";
"tour_section_6_text_7" = "You can also add another's playlists to your collection";


"tour_section_7_title_1" = "Follow what your friends write";
"tour_section_7_text_1" = "The &quot;My News&quot; section is divided into two types: local feed and global feed";
"tour_section_7_text_2" = "The local feed will only show news from your friends and groups";
"tour_section_7_bottom_text_1" = "No recommendation system. <b>It's up to you to create your own news feed</b>";


"tour_section_7_title_1" = "Stay Updated with Your Friends' Updates";
"tour_section_7_text_1" = "The 'My News' section is divided into two types: local feed and global feed.";
"tour_section_7_text_2" = "The local feed exclusively displays updates from your friends and groups.";
"tour_section_7_bottom_text_1" = "No recommendation system. <b>You have the power to curate your own news feed</b>.";

"tour_section_8_title_1" = "Explore Site-wide Discussions";
"tour_section_8_text_1" = "The global newsfeed showcases posts from all site users and groups.";
"tour_section_8_text_2" = "Note: Viewer discretion is advised, as this section may contain content not suitable for sensitive individuals.";
"tour_section_8_bottom_text_1" = "The design of the global feed is identical to the local feed.";
"tour_section_8_bottom_text_2" = "The feed encompasses various content types, including regular photos, videos, anonymous posts, and polls.";


"tour_section_9_title_1" = "Create Groups!";
"tour_section_9_text_1" = "Explore thousands of groups on the site covering diverse topics and fan communities.";
"tour_section_9_text_2" = "Join any group that interests you, and if you can't find one, feel free to create your own.";
"tour_section_9_text_3" = "Each group comes with its dedicated section of wiki pages, photo albums, a block of links, and discussion forums.";
"tour_section_9_title_2" = "Manage Your Group with a Friend";
"tour_section_9_text_2_1" = "Navigate to the &quot;Edit Group&quot; section under the community avatar to manage your group.";
"tour_section_9_text_2_2" = "Build a team of administrators from regular participants or individuals you trust.";
"tour_section_9_text_2_3" = "You have the option to hide specific administrators, ensuring they remain discreet within your group.";
"tour_section_9_bottom_text_1" = "Find the &quot;My Groups&quot; section in the left menu of the site.";
"tour_section_9_bottom_text_2" = "Example Group";
"tour_section_9_bottom_text_3" = "Groups often represent real organizations, with members wanting to stay connected with their audiences.";

"tour_section_10_title_1" = "Oops";
"tour_section_10_text_1" = "I would be very happy to do a tutorial on this section, but it's still under development. For now, let's skip it and move on...";


"tour_section_11_title_1" = "Themes";
"tour_section_11_text_1" = "Upon registration, the default theme will be set as your appearance.";
"tour_section_11_text_2" = "Some new users might find the current default theme a bit dated.";
"tour_section_11_text_3" = "<b>No worries:</b> You can create your own theme by reading the <a href='https://docs.ovk.to/'>documentation</a> or choose one from the catalog.";
"tour_section_11_bottom_text_1" = "A catalog of themes is available under \"My Settings\" in the \"Interface\" tab.";
"tour_section_11_wordart" = "<img src='/assets/packages/static/openvk/img/tour/wordart_en.png' width='65%'>";

"tour_section_12_title_1" = "Profile and Group Backgrounds";
"tour_section_12_text_1" = "You can set two images as the background of your page.";
"tour_section_12_text_2" = "They will be displayed on the sides for those who visit your page.";
"tour_section_12_text_3" = "<b>Hint:</b> Before setting the background, experiment with the layout—try mirroring the future background image or creating a nice gradient.";
"tour_section_12_title_2" = "Avatars";
"tour_section_12_text_2_1" = "You can choose to display the user's avatar in standard, rounded, or square (1:1) styles.";
"tour_section_12_text_2_2" = "These settings are visible only to you.";
"tour_section_12_title_3" = "Editing the Left Menu";
"tour_section_12_text_3_1" = "Hide unnecessary sections of the site if needed.";
"tour_section_12_text_3_2" = "<b>Remember:</b> Essential sections like My Page, My Friends, My Answers, and My Settings cannot be hidden.";
"tour_section_12_title_4" = "View of Posts";
"tour_section_12_text_4_1" = "If you're tired of the old wall design from the once-popular original VKontakte, change the post look to Microblog.";
"tour_section_12_text_4_2" = "You can switch between the two post views at any time.";
"tour_section_12_text_4_3" = "<b>Note:</b> If you choose the old post view, the latest comments will not be loaded.";
"tour_section_12_bottom_text_1" = "Background Setup Page";
"tour_section_12_bottom_text_2" = "Examples of Pages with Set Backgrounds";
"tour_section_12_bottom_text_3" = "Add more personality to your profile with this feature.";
"tour_section_12_bottom_text_4" = "Old Post View";
"tour_section_12_bottom_text_5" = "Microblog";


"tour_section_13_title_1" = "Promocodes";
"tour_section_13_text_1" = "OpenVK features a promo code system designed to add various currencies such as rating percentages, votes, and more.";
"tour_section_13_text_2" = "These coupons are created for significant events and holidays. Stay updated on the latest by following the <a href='https://t.me/openvk'>OpenVK Telegram channel</a>.";
"tour_section_13_text_3" = "Upon activating a promo code, the currency determined by administrators will be transferred to your account.";
"tour_section_13_text_4" = "<b>Remember:</b> All promo codes have a limited activation period.";
"tour_section_13_bottom_text_1" = "Promo codes consist of 24 digits and letters.";
"tour_section_13_bottom_text_2" = "Successful activation, for example, results in the crediting of 100 votes.";
"tour_section_13_bottom_text_3" = "<b>Attention:</b> Once a promo code is activated on your page, the same code cannot be reactivated.";

"tour_section_14_title_1" = "Mobile Version";
"tour_section_14_text_1" = "Currently, there is no mobile web version of the site, but there is an Android mobile app.";
"tour_section_14_text_2" = "OpenVK Legacy is an OpenVK mobile app designed for retro Android devices, featuring the design of the original VKontakte 3.0 app from 2013.";
"tour_section_14_text_3" = "The minimum supported version is Android 2.1 Eclair, making devices from the early 2010s quite useful.";

"tour_section_14_title_2" = "Where to Download?";
"tour_section_14_text_2_1" = "Release versions can be downloaded through the official F-Droid repository.";
"tour_section_14_text_2_2" = "If you're a beta tester, new app versions are posted to a separate update channel.";
"tour_section_14_text_2_3" = "<b>Note:</b> The app may have various bugs and issues. If you encounter an error, please report it to the <a href='/app'>official app group</a>.";

"tour_section_14_bottom_text_1" = "Screenshots";
"tour_section_14_bottom_text_2" = "That concludes the tour of the site. If you're interested in trying our mobile app, creating your group, inviting friends, or just having fun in general, you can do it now with a quick <a href='/reg'>registration</a>.";
"tour_section_14_bottom_text_3" = "This concludes the site tour.";

/* Search */

"s_params" = "Search params";
"s_people" = "Users";
"s_groups" = "Clubs";
"s_apps" = "Applications";
"s_posts" = "Posts";
"s_comments" = "Comments";
"s_photos" = "Photos";
"s_videos" = "Videos";
"s_audios" = "Music";
"s_audios_playlists" = "Playlists";
"s_documents" = "Documents";

"s_by_people" = "for users";
"s_by_groups" = "for groups";
"s_by_posts" = "for posts";
"s_by_comments" = "for comments";
"s_by_videos" = "for videos";
"s_by_apps" = "for apps";
"s_by_audios" = "for audios";
"s_by_audios_playlists" = "for playlists";
"s_by_documents" = "for documents";

"s_order_by" = "Order by...";

"s_order_by_id" = "By id";
"s_order_by_name" = "By name";
"s_order_by_random" = "By random";
"s_order_by_rating" = "By rating";
"s_order_by_length" = "By length";
"s_order_by_listens" = "By listens count";
"s_order_invert" = "Invert";
"s_order_by_reg_date" = "By reg date";
"s_order_by_creation_date" = "By creation date";
"s_order_by_publishing_date" = "By publication date";
"s_order_by_upload_date" = "By upload date";

"s_by_date" = "By date";
"s_registered_before" = "Registered before";
"s_registered_after" = "Registered after";
"s_date_before" = "Before";
"s_date_after" = "After";

"s_main" = "Main";
"s_type" = "Type";

"s_now_on_site" = "now on site";
"s_with_photo" = "with photo";
"s_only_in_names" = "only in names";
"s_only_youtube" = "only from YouTube";

"s_any" = "any";
"s_any_single" = "any";
"reset" = "Reset";

"closed_group_post" = "This post was published in a private group";
"deleted_target_comment" = "This comment belongs to deleted post";

"no_results" = "No results";
"s_only_performers" = "By performers";
"s_with_lyrics" = "With lyrics";

"showing_x_y" = "(showing $1—$2)";
"no_results_by_this_query" = "Nothing was found by this query.";
"s_additional" = "Additional";
"s_it_is_you" = "that's you";

/* BadBrowser */

"deprecated_browser" = "Deprecated Browser";
"deprecated_browser_description" = "To view this content, you will need Firefox ESR 52+ or an equivalent World Wide Web navigator. Sorry about that.";

/* Statistics */

"coverage" = "Coverage";
"coverage_this_week" = "This graph shows the coverage over the last 7 days.";
"views" = "Views";
"views_this_week" = "This graph shows the views of community posts over the last 7 days.";

"full_coverage" = "Full coverage";
"all_views" = "All views";

"subs_coverage" = "Subscribers coverage";
"subs_views" = "Subscribers views";

"viral_coverage" = "Viral coverage";
"viral_views" = "Viral views";

/* Sudo */

"you_entered_as" = "You are logged in as";
"please_rights" = "please respect the right to privacy of other people's correspondence and do not abuse user swapping.";
"click_on" = "Click";
"there" = "here";
"to_leave" = "to log out";

/* Phone number */

"verify_phone_number" = "Confirm phone number";
"we_sended_first" = "We sent an SMS with code to the given number";
"we_sended_end" = "enter it here";

/* Mobile */
"mobile_friends" = "Friends";
"mobile_photos" = "Photos";
"mobile_audios" = "Audios";
"mobile_videos" = "Videos";
"mobile_messages" = "Messages";
"mobile_notes" = "Notes";
"mobile_groups" = "Groups";
"mobile_search" = "Search";
"mobile_settings" = "Settings";
"mobile_desktop_version" = "Desktop version";
"mobile_log_out" = "Log out";
"mobile_menu" = "Menu";
"mobile_like" = "Like";
"mobile_user_info_hide" = "Hide";
"mobile_user_info_show_details" = "Show details";
"mobile_attachment_only_for_pc" = "This attachments is not implemented for PDA version. Please, view it on PC.";

/* Fullscreen player */

"hide_player" = "Minimize";
"close_player" = "Close";
"show_comments" = "Show info";
"close_comments" = "Hide info";
"to_page" = "Go to page";
"download_video" = "Download";
"added" = "Added";
"x_views" = "$1 views";

"video_author" = "Video's author";
"video_delete" = "Delete";
"no_description" = "no description";

"show_more_comments" = "Show more comments";
"video_processing" = "Video is succefully uploaded and now is processed.";
"video_access_denied" = "Access to video denied";
"open_page_to_read_comms" = "To read the comments, open <a href=\"$1\">page</a>.";

"no_video_error" = "No videofile";
"no_video_description" = "Select file or specify link.";

"error_video" = "An error has occurred";
"file_corrupted" = "File is corrupted or does not have video.";
"link_incorrect" = "Maybe, the link is wrong.";

"no_name_error" = "Video can't be published without name";
"access_denied_error" = "Access denied";
"access_denied_error_description" = "You are not allowed to edit this resource";

"changes_saved_video_comment" = "Updated data will appear on the video page";
"cant_delete_video" = "Failed to delete video";
"cant_delete_video_comment" = "You are not logged in.";

"change_video" = "Change video";

"video_is_deleted" = "Video was deleted.";
"share_video" = "Share video";
"shared_succ_video" = "Video will appear at your wall. Click on this notification to move to post.";
"watch_in_window" = "Watch in window";

"comments_load_timeout" = "The instance may have fallen";

"my" = "My";
"enter_a_name_or_artist" = "Enter a name or artist...";

/* Moderation */

"section" = "Section";
"template_ban" = "Ban by template";
"active_templates" = "Active templates";
"users_reports" = "Users reports";
"substring" = "Substring";
"n_user" = "User";
"time_before" = "Time earlier than";
"time_after" = "Time later than";
"where_for_search" = "WHERE for search by section";
"block_params" = "Block params";
"only_rollback" = "Only rollback";
"only_block" = "Only blocking";
"rollback_and_block" = "Rollback and blocking";
"subm" = "Apply";

"select_section_for_start" = "Choose a section to get started";
"results_will_be_there" = "Search results will be displayed here";
"search_results" = "Search results";
"cnt" = "pcs";

"link_to_page" = "Link on page";
"or_subnet" = "or subnet";
"error_when_searching" = "Error while executing request";
"no_found" = "No found";
"operation_successfully" = "Operation completed successfully";

"unknown_error" = "Unknown error";
"templates" = "Template";
"type" = "Type";
"count" = "Count";
"time" = "Time";

"roll_back" = "rollback";
"roll_backed" = "rollbacked";

"nospam_prevention" = "This action will affect a lot of data. Are you sure you want to apply?";

/* RSS */

"post_deact_in_general" = "Page deletion";
"upd_in_general" = "Avatar update";
"on_wall" = "On wall";
"sign_short" = "Sign";

/* Documents */

"my_documents" = "Documents";
"my_documents_objectively" = "My Documents";
"documents_of_group" = "Group's documents";
"search_by_documents" = "Search for documents..";
"documents" = "Documents";
"document_uploading_in_general" = "Upload document";
"document_editing_in_general" = "Edit document";
"file" = "File";
"tags" = "Tags";
"owner_is_hidden" = "Hide author";
"accessbility" = "Accessbility";
"download_file" = "Download file";
"remove" = "Remove";

"document" = "Document";
"documents_all" = "All documents";
"document_type_0" = "All";
"document_type_1" = "Text";
"document_type_2" = "Archives";
"document_type_3" = "GIF";
"document_type_4" = "Images";
"document_type_5" = "Audio";
"document_type_6" = "Video";
"document_type_7" = "Books";
"document_type_8" = "Another";

"documents_one" = "$1 document";
"documents_few" = "$1 documents";
"documents_many" = "$1 documents";
"documents_other" = "$1 documents";
"documents_zero" = "$1 documents";

"you_have_x_documents_one" = "You have $1 document";
"you_have_x_documents_few" = "You have $1 documents";
"you_have_x_documents_many" = "You have $1 documents";
"you_have_x_documents_other" = "You have $1 documents";
"you_have_x_documents_zero" = "You have $1 documents";

"group_has_x_documents_one" = "This group has $1 document";
"group_has_x_documents_few" = "This group has $1 documents";
"group_has_x_documents_many" = "This group has $1 documents";
"group_has_x_documents_other" = "This group has $1 documents";
"group_has_x_documents_zero" = "This group has $1 documents";

"x_documents_in_tab_one" = "$1 document at this tab";
"x_documents_in_tab_few" = "$1 documents at this tab";
"x_documents_in_tab_many" = "$1 documents at this tab";
"x_documents_in_tab_other" = "$1 documents at this tab";
"x_documents_in_tab_zero" = "$1 documents at this tab";

"there_is_no_documents_alright" = "There is no documents.";
"limitations_file_limit_size" = "File must not exceed $1 MB";
"limitations_file_allowed_formats" = "Allowed formats";
"limitations_file_author_rights" = "File must not violate copyright and site rules";
"select_file_fp" = "Select file";
"error_file_too_big" = "File is too big.";
"error_file_invalid_format" = "File format is not allowed.";
"error_file_adding_copied" = "File is already added.";
"error_file_preview" = "Error when uploading file: weird image.";

"private_document" = "Private (by link)";
"public_document" = "Public";
"documents_sort_add" = "By date";
"documents_sort_alphabet" = "A-Z";
"documents_sort_size" = "By size";
"select_doc" = "Attach document";
"no_documents" = "No documents found";
"go_to_my_documents" = "Go to own documents";

/* Fave */

"faves" = "Bookmarks";
"faves_empty_tip" = "There will be your liked content.";
"faves_posts_empty_tip" = "There will be posts liked by you.";
"faves_comments_empty_tip" = "There will be comments liked by you.";
"faves_photos_empty_tip" = "There will be photos liked by you.";
"faves_videos_empty_tip" = "There will be videos liked by you.";
"faves_zero" = "No bookmarks";
"faves_one" = "One bookmark";
"faves_few" = "$1 bookmarks";
"faves_many" = "$1 bookmarks";
"faves_other" = "$1 bookmarks";
