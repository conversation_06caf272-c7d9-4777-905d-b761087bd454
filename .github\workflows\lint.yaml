name: Lint

on:
  push:
  pull_request:

jobs:
  lint:
    runs-on: ubuntu-latest
    
    # 'push' runs on inner branches, 'pull_request' will run only on outer PRs
    if: >
      github.event_name == 'push'
      || (github.event_name == 'pull_request'
          && github.event.pull_request.head.repo.full_name != github.repository)

    permissions:
      contents: read
    steps:
      - name: Code Checkout
        uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: "8.2"
          extensions: gd, zip, intl, yaml, pdo_mysql, rdkafka, imagick
          tools: composer:v2
          coverage: none

      - name: Install dependencies
        run: composer install --no-interaction --no-progress --no-suggest --prefer-dist

      - name: PHP CS Fixer
        run: vendor/bin/php-cs-fixer fix --dry-run --diff
