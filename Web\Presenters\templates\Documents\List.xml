{extends "../@layout.xml"}

{block title}
    {if !isset($group)}
        {_my_documents_objectively}
    {else}
        {_documents_of_group}
    {/if}
{/block}

{block header}
    {if !isset($group)}
        {_my_documents}
    {else}
        <a href="{$group->getURL()}">{$group->getCanonicalName()}</a> »
        {_my_documents}
    {/if}
{/block}

{block content}
    {var $is_gallery = $current_tab == 3 || $current_tab == 4}
    <div id="docs_page_wrapper">
        <div class="docs_page_tabs">
            <div class="mb_tabs display_flex_row display_flex_space_between">
                <div>
                    <div class="mb_tab" n:attr="id => $current_tab == 0 ? active">
                        <a href="?tab=0">{_document_type_0}</a>
                    </div>
                    <div n:foreach="$tabs as $tab" class="mb_tab" n:attr="id => $tab['type'] == $current_tab ? active">
                        <a href="?tab={$tab['type']}">
                            {$tab["name"]}
                            <span n:if="$tab['count'] > 1" class="special_counter">{$tab["count"]}</span>
                        </a>
                    </div>
                </div>

                <input n:if="$canUpload" id="upload_entry_point" class="button" type="button" value="{_upload_button}" {if isset($group)}data-gid="{$group->getId()}"{/if}>
            </div>
        </div>
        <div n:class="docs_page_content, $is_gallery ? docs_page_gallery">
            <div n:if="$count > 0" class="summaryBar display_flex_row display_flex_space_between">
                <div class="summary">{tr($locale_string, $count)}.</div>

                <select n:if="$count > 3" name="docs_sort">
                    <option n:attr="selected => $order == 0" value="0">{_documents_sort_add}</option>
                    <option n:attr="selected => $order == 1" value="1">{_documents_sort_alphabet}</option>
                    <option n:attr="selected => $order == 2" value="2">{_documents_sort_size}</option>
                </select>
            </div>
            <div n:attr="id => !$is_gallery && sizeof($tags) > 0 ? search_page">
                <div n:class="container_white, scroll_container, !$is_gallery && sizeof($tags) > 0 ? page_wrap_content_main">
                    {if $count > 0}
                        {foreach $docs as $doc}
                            {if $is_gallery}
                                {include "components/image.xml", doc => $doc, scroll_context => true, club => isset($group) ? $group : NULL}
                            {else}
                                {include "components/doc.xml", doc => $doc, scroll_context => true, club => isset($group) ? $group : NULL}
                            {/if}
                        {/foreach}
                    {else}
                        {include "../components/error.xml", description => tr("there_is_no_documents_alright")}
                    {/if}
                </div>

                <div n:if="!$is_gallery && sizeof($tags) > 0" class='page_wrap_content_options verticalGrayTabsWrapper'>
                    <div class="page_wrap_content_options_list verticalGrayTabs with_padding">
                        <a id="used">{_documents_all}</a>
                        {foreach $tags as $tag}
                            <a href="/search?section=docs&tags={urlencode($tag)}">{$tag}</a>
                        {/foreach}
                    </div>
                </div>
            </div>
            {include "../components/paginator.xml", conf => $paginatorConf}
        </div>
    </div>
{/block}
