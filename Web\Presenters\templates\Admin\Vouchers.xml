{extends "@layout.xml"}

{block title}
    {_vouchers}
{/block}

{block headingWrap}
    <a style="float: right;" class="aui-button aui-button-primary" href="/admin/vouchers/id0">
        {_create}
    </a>

    <h1>{_vouchers}</h1>
{/block}

{block content}
    <table class="aui aui-table-list">
        <thead>
            <tr>
                <th>ID</th>
                <th>{_admin_voucher_serial}</th>
                <th>{_coins}</th>
                <th>{_admin_voucher_rating}</th>
                <th>{_usages_left}</th>
                <th>{_admin_voucher_status}</th>
                <th>{_admin_actions}</th>
            </tr>
        </thead>
        <tbody>
            <tr n:foreach="$vouchers as $voucher">
                <td>{$voucher->getId()}</td>
                <td>{$voucher->getFormattedToken()}</td>
                <td>{$voucher->getCoins()}&#xa2;</td>
                <td>{$voucher->getRating()}</td>
                <td>{$voucher->getRemainingUsages() === INF ? "∞" : $voucher->getRemainingUsages()}</td>
                <td>
                    {if $voucher->isExpired()}
                        <span class="aui-lozenge aui-lozenge-subtle aui-lozenge-removed">{_admin_voucher_status_closed}</span>
                    {else}
                        <span class="aui-lozenge aui-lozenge-subtle aui-lozenge-success">{_admin_voucher_status_opened}</span>
                    {/if}
                </td>
                <td>
                    <a class="aui-button aui-button-primary" href="/admin/vouchers/id{$voucher->getId()}">
                        <span class="aui-icon aui-icon-small aui-iconfont-new-edit">{_edit}</span>
                    </a>
                </td>
            </tr>
        </tbody>
    </table>
    <br/>

    <div align="right">
        {var $isLast = ((20 * (($_GET['p'] ?? 1) - 1)) + sizeof($vouchers)) < $count}

        <a n:if="($_GET['p'] ?? 1) > 1" class="aui-button" href="?p={($_GET['p'] ?? 1) - 1}">&laquo;</a>
        <a n:if="$isLast" class="aui-button" href="?p={($_GET['p'] ?? 1) + 1}">&raquo;</a>
    </div>
{/block}
