{extends "../@listView.xml"}
{var $iterator = iterator_to_array($albums)}
{var $page     = $paginatorConf->page}

{block title}{_albums} {$owner->getCanonicalName()}{/block}

{block header}
    {if isset($thisUser) && $thisUser->getId() == $owner->getRealId()}
        {_my_photos}
    {else}
        <a href="{$owner->getURL()}">
        {$owner->getCanonicalName()}</a>
        »
        {_albums}
    {/if}
{/block}

{block size}
    <div style="padding-bottom: 0px; padding-top: 0;" class="summaryBar">
        <div class="summary">
            {if !is_null($thisUser) && $owner->getRealId() === $thisUser->getId()}
                {tr("albums_list", $count)}
            {else}
                {tr("albums", $count)}
            {/if}

            <span n:if="$canEdit" style="float: right;">
                &nbsp;|&nbsp;
                {var $isClub = ($owner instanceof \openvk\Web\Models\Entities\Club)}
                <a href="/albums/create{$isClub ? '?gpid=' . $owner->getId() : ''}">{_create_album}</a>
            </span>
        </div>
    </div>
{/block}

{block actions}

{/block}

{* BEGIN ELEMENTS DESCRIPTION *}

{block link|strip|stripHtml}
    /album{$x->getPrettyId()}
{/block}

{block preview}
    {var $cover   = $x->getCoverPhoto()}
    {var $preview = is_null($cover) ? "/assets/packages/static/openvk/img/camera_200.png" : $cover->getURLBySizeId("normal")}

    <a href="/album{$x->getPrettyId()}">
        <img src="{$preview}" alt="{$x->getName()}" style="height: 130px; width: 170px; object-fit: cover" loading=lazy />
    </a>
{/block}

{block name}
    {$x->getName()}
{/block}

{block description}
    <span>{$x->getDescription() ?? $x->getName()}</span><br />
    <span style="color: grey;">{$x->getPhotosCount()} {_photos}</span><br />
    <span style="color: grey;">{tr("updated_at", $x->getEditTime() ?? $x->getCreationTime())}</span><br />
    <span style="color: grey;">{_created} {$x->getCreationTime()}</span><br />
{/block}