body {
    background: url("/themepack/openvk_modern/*******/resource/1.png") repeat-x
        fixed;
}

.page_header {
    position: fixed;
    height: 42px;
    background: #3c3c3c;
    z-index: 199;
}

.home_button {
    background: #3c3c3c url("/themepack/openvk_modern/*******/resource/2.png")
        no-repeat;
    background-size: 80%;
    background-position-y: 0px;
    background-position-x: 1px;
}

.home_button_custom {
    background: #3c3c3c url("/themepack/openvk_modern/*******/resource/4.png")
        no-repeat;
    background-size: 80%;
    background-position-y: 0px;
    background-position-x: 1px;
    width: 145px !important;
    text-shadow: none;
}

.header_navigation .link,
.header_navigation .header_divider_stick {
    background: unset !important;
}

.header_navigation .link a:hover {
    text-decoration: none;
}

.header_navigation #search_box .search_box_button {
    border: solid 1px #606060;
    box-shadow: unset;
}

.header_navigation #search_box .search_box_button:active {
    background-color: #606060;
    box-shadow: unset;
}

.sidebar {
    margin-top: 47px;
    position: fixed;
    background-color: #fff;
    z-index: 199;
}

.page_body {
    margin-top: 42px;
}

.toTop {
    margin-top: 42px;
}

.content_title_expanded {
    cursor: pointer;
    background-image: unset !important;
    padding: 3px 10px;
    border-top: #e6e6e6 solid 1px;
}

.content_title_unexpanded {
    background-image: unset !important;
    padding: 3px 10px;
    border-top: #eee solid 1px;
}

.content_subtitle {
    border-top: #f0f0f0 solid 1px;
    border-bottom: 1px solid #f0f0f0;
}

.user-alert {
    border: 1px solid #f3ddbd;
}

.msg {
    border: 1pt solid #e6f2f3;
}

.msg.msg_succ {
    border-color: #ddf3d7;
}

.msg.msg_err {
    border-color: #f5e9ec;
}

.navigation .link:hover {
    border-top: 1px solid #e4e4e4;
}

#profile_link,
.profile_link {
    border-bottom: 1px solid transparent;
}

.completeness-gauge {
    width: 100%;
    border: unset;
    border-top: unset;
}

.post-author {
    border-top: #fff solid 1px;
    border-bottom: #fff solid 1px;
    background-color: #fff;
    padding: 0px 5px 3px;
}

.post-author .date {
    color: gray;
}

.page_yellowheader {
    background: #e2e2e2;
    border-right: solid 1px #e2e2e2;
    border-left: solid 1px #e2e2e2;
    border-bottom: solid 1px #e2e2e2;
}

.page_yellowheader span {
    color: #bbbbbb;
}

.page_yellowheader a {
    color: #5c5c5c;
}

.page-wrap {
    border-bottom: solid 1px #fff;
    border-left: solid 1px #fff;
    border-right: solid 1px #fff;
}

.page_wrap {
    border-bottom: solid 1px #fff;
    border-left: solid 1px #fff;
    border-right: solid 1px #fff;
}

#wrapHI {
    border-right: solid 1px #e2e2e2;
    border-left: solid 1px #e2e2e2;
}

.left_small_block {
    border-right: 1px #fff solid;
}

.menu_divider {
    background: #e5e5e5;
}

.postFeedWrapper {
    border-bottom: 1px solid rgb(240, 240, 240);
}

.container_gray {
    border-top: #ebebeb solid 1px;
}

.container_gray .content {
    border: #e5e5e5 solid 1px;
}

.accent-box {
    border: 1px solid white;
}

input[type="text"],
input[type="password"],
input[type~="text"],
input[type~="password"],
input[type="email"],
input[type="phone"],
input[type~="email"],
input[type~="phone"],
input[type="date"],
input[type~="date"],
input[type="search"],
input[type~="search"],
textarea,
select {
    border: 1px solid #e5e5e5;
}

input[type="checkbox"] {
    background-image: url("/themepack/openvk_modern/*******/resource/6.png");
}

ul {
    list-style: none;
    list-style-type: disc;
}

.like_tooltip_wrapper .like_tooltip_head {
    background: #515151;
    box-shadow: unset;
    border: solid 1px #515151;
}

.like_tooltip_wrapper .like_tooltip_body {
    border: 1px solid #515151;
}

.mb_tab#active div {
    border: 2px solid #898989;
}

.summaryBar {
    border-bottom: #fff solid 1px;
}

.page_footer .link:hover {
    border-top: 0px;
}

.ovk-video > .preview {
    border: #fff;
}

.crp-list {
    border-top: 1px solid #fff;
    width: 629px;
}

.crp-entry:first-of-type {
    border-color: #e5e5e5;
}

.crp-entry {
    width: 593px;
    border-color: #e5e5e5;
}

#faqhead {
    border: 1px solid #fbf3c3;
}

#faqcontent {
    border: 1px solid #fafafa;
}

.ovk-diag {
    border: none;
    border-radius: 2px;
}

.ovk-diag-cont {
    border-radius: 2px;
}

.ovk-diag-head {
    border-bottom: 1px solid #757575;
    border-top-left-radius: 2px;
    border-top-right-radius: 2px;
}

.ovk-diag-action {
    border-bottom-left-radius: 2px;
    border-bottom-right-radius: 2px;
}

#votesBalance,
#news {
    border-bottom: unset;
}

.floating_sidebar,
.floating_sidebar.show {
    display: none;
}

#backdrop:before {
    content: "";
    display: block;
    position: fixed;
    top: 0;
    left: 0;
    height: 42px;
    width: 100%;
    background-color: #3c3c3c;
}

.search_box_button {
    box-shadow: none;
}

.search_box_button:active {
    box-shadow: none;
}

.verticalGrayTabs #used {
    background: #3c3c3c !important;
    border: 1px solid #3c3c3c;
}

.verticalGrayTabs #used a {
    color: white;
}

.search_option_name {
    background-color: #a4a4a4;
    border-bottom: unset;
}

.verticalGrayTabsWrapper {
    border-top: unset;
    border-left: unset;
}

.sugglist {
    border-top: unset;
    border-bottom: 1px solid gray;
}

.musicIcon {
    filter: contrast(200%) !important;
}

.audioEntry .playerButton .playIcon {
    filter: contrast(2) !important;
}

.audioEmbed .track > .selectableTrack,
.bigPlayer .selectableTrack {
    border-top: #404040 1px solid !important;
}

.bigPlayer .paddingLayer .slider,
.audioEmbed .track .slider {
    background: #3c3c3c !important;
}

.audioEntry.nowPlaying {
    background: #4b4b4b !important;
}

.audioEntry.nowPlaying:hover {
    background: #373737 !important;
}

.musicIcon.pressed {
    filter: brightness(150%) !important;
}

.musicIcon.lagged {
    opacity: 50%;
}

.bigPlayer {
    position: sticky;
    top: 42px;
    box-shadow: unset;
}

#audio_upload {
    border: 1px solid #ccc;
}

#wallAttachmentMenu {
    box-shadow: unset;
}

#backdropEditor {
    border: unset;
}
