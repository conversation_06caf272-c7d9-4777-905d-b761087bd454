{extends "../@listView.xml"}
{var $iterator = $videos}
{var $count    = $paginatorConf->count}
{var $page     = $paginatorConf->page}

{block title}{_videos} {$user->getCanonicalName()}{/block}

{block header}
    <a href="{$user->getURL()}">{$user->getCanonicalName()}</a>
    » {_videos}
{/block}

{block size}
    <div style="padding-bottom: 0px; padding-top: 0;" class="summaryBar">
        <div class="summary">
            {tr("videos", $count)}
            <span n:if="isset($thisUser) && $thisUser->getId() == $user->getId() && !OPENVK_ROOT_CONF['openvk']['preferences']['videos']['disableUploading']">
                &nbsp;|&nbsp;
                <a href="/videos/upload">{_upload_video}</a>
            </span>
        </div>
    </div>
{/block}

{block actions}
    
{/block}

{* BEGIN ELEMENTS DESCRIPTION *}

{block link|strip|stripHtml}
    /video{$x->getPrettyId()}
{/block}

{block preview}
    <div class="video-preview" id="videoOpen" data-id="{$x->getPrettyId()}">
        <img src="{$x->getThumbnailURL()}"
            alt="{$x->getName()}"
            style="max-width: 170px; max-height: 127px; margin: auto;" />
    </div>
{/block}

{block name}
    <span id="videoOpen" data-id="{$x->getPrettyId()}" style="color:unset;">{$x->getName()}</span>
{/block}

{block description}
    <p>
        <span>{$x->getDescription() ?? ""}</span>
    </p>
    <span style="color: grey;">{_video_uploaded} {$x->getPublicationTime()}</span><br/>
    <span style="color: grey;">{_video_updated} {$x->getEditTime() ?? $x->getPublicationTime()}</span>
    <p>
        <a href="/video{$x->getPrettyId()}" data-id="{$x->getPrettyId()}">{_view_video}</a>
        {if $x->getCommentsCount() > 0}| <a href="/video{$x->getPrettyId()}#comments">{_comments} ({$x->getCommentsCount()})</a>{/if}
    </p>
{/block}
