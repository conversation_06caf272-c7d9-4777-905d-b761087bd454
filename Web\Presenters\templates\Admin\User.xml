{extends "@layout.xml"}

{block title}
    {_edit} {$user->getCanonicalName()}
{/block}

{block heading}
    {$user->getCanonicalName()}
{/block}

{block content}
    <div class="aui-tabs horizontal-tabs">
        <form class="aui" method="POST">
            <div class="field-group">
                <label for="avatar">{_avatar}</label>
                <span id="avatar" class="aui-avatar aui-avatar-project aui-avatar-xlarge">
                    <span class="aui-avatar-inner">
                        <img src="{$user->getAvatarUrl('tiny')}" style="object-fit: cover;"></img>
                    </span>
                </span>
            </div>
            <div class="field-group">
                <label for="id">ID</label>
                <input class="text medium-field" type="number" id="id" disabled value="{$user->getId()}" />
            </div>
            <div class="field-group">
                <label for="guid">GUID</label>
                <input class="text medium-field" id="guid" disabled value="{$user->getChandlerUser()->getId()}" />
            </div>
            <div class="field-group">
                <label for="registration_ip">{_admin_first_known_ip}</label>
                <input class="text medium-field" id="guid" disabled value="{$user->getRegistrationIP()}" />
            </div>
            <div class="field-group">
                <label for="first_name">{_name}</label>
                <input class="text medium-field" type="text" id="first_name" name="first_name" value="{$user->getFirstName()}" />
            </div>
            <div class="field-group">
                <label for="last_name">{_surname}</label>
                <input class="text medium-field" type="text" id="last_name" name="last_name" value="{$user->getLastName()}" />
            </div>
            <div class="field-group">
                <label for="nickname">{_nickname}</label>
                <input class="text medium-field" type="text" id="nickname" name="nickname" value="{$user->getPseudo()}" />
            </div>
            <div class="field-group">
                <label for="status">{_status}</label>
                <input class="text medium-field" type="text" id="status" name="status" value="{$user->getStatus()}" />
            </div>
            <div class="field-group">
                <label for="email">E-Mail</label>
                <input class="text medium-field" type="email" id="email" name="email" value="{$user->getEmail()}" />
            </div>
            <div class="field-group">
                <label for="shortcode">{_admin_shortcode}</label>
                <input class="text medium-field" type="text" id="shortcode" name="shortcode" value="{$user->getShortCode()}" />
            </div>
            <hr>
            <div class="field-group">
                <label for="city">{_admin_verification}</label>
                <input class="toggle-large" type="checkbox" id="verify" name="verify" value="1" {if $user->isVerified()} checked {/if} />
            </div>
            <div class="field-group">
             <label for="city">{_admin_hide_global_feed}</label>
                <input class="toggle-large" type="checkbox" id="hide_global_feed" name="hide_global_feed" value="1" n:attr="checked => $user->HideGlobalFeed()" />
            </div>
            <div class="field-group">
                <label for="city">{_admin_user_online}</label>
                <select name="online" class="select">
                    <option value="0" {if $user->onlineStatus() > 2}selected{/if}>{_admin_user_online_default}</option>
                    <option value="1" {if $user->onlineStatus() == 1}selected{/if}>{_admin_user_online_incognite}</option>
                    <option value="2" {if $user->onlineStatus() == 2}selected{/if}>{_admin_user_online_deceased}</option>
                </select>
            </div>
            <hr/>
            <div class="field-group">
                <label for="email">{_password}</label>
                <input class="text medium-field" type="password" id="password" name="password" value="" />
            </div>
            <div class="buttons-container">
                <div class="buttons">
                    <a class="button" onclick="let pswd = Math.random().toString(27).slice(2,12); alert('Сгенерированный пароль: ' + pswd + '\n\nНе забудьте сообщить пользователю, чтобы он сменил пароль на свой!'); $('input#password').val(pswd);">Сгенерировать пароль</a>
                </div>
            </div>
            <hr/>
            <h2>{_c_groups}</h2>
            <div>
                <div class="field-group">
                    <label for="add-to-group">{_c_add_to_group}</label>
                    <select class="select" name="add-to-group">
                        <option n:foreach="$c_groups_list as $group" value="{$group->id}">
                            {$group->name}
                        </option>
                    </select>

                    <table class="aui aui-table-list">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>{_admin_actions}</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr n:foreach="$c_memberships as $membership">
                                <td>
                                    <a href="/admin/chandler/groups/{$membership->group}?act=members">{$membership->group}</a>
                                </td>
                                <td>
                                    <a
                                        class="aui-icon aui-icon-small aui-iconfont-cross"
                                        href="/admin/chandler/groups/{$membership->group}?act=removeMember&uid={$user->getChandlerGUID()}"
                                        style="margin: 0 50%;"
                                    >
                                        {_c_remove_from_group}
                                    </a>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="buttons-container">
                <div class="buttons">
                    <input type="hidden" name="hash" value="{$csrfToken}" />
                    <input class="button submit" type="submit" value="{_save}">
                </div>
            </div>
        </form>
    </div>
{/block}
