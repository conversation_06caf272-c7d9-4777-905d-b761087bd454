{extends "../@layout.xml"}
{var $canReport = $owner->getId() !== $thisUser->getId()}

{block title}
    {$name}
{/block}

{block header}
    {$name}
    <a style="float: right;" onClick="reportApp({$id})" n:if="$canReport ?? false">{_report}</a>
{/block}

{block content}
    <center>
        <iframe id="appFrame" referrerpolicy="unsafe-url" frameBorder="0" src="{$url}" height="600" width="600"
            sandbox="allow-scripts allow-same-origin allow-pointer-lock allow-forms allow-downloads-without-user-activation"></iframe>
    </center>

    <div n:if="!is_null($news)" id="news">
        <h4>{$news->getName()}</h4>
        <div id="app_news_container">
            {$news->getText()|noescape}
        </div>
    </div>

    <center>
        <p>
            {_app_dev}: <a href="{$owner->getURL()}">{$owner->getFullName()}</a>
        </p>
    </center>

    <script>
        window.appId     = {$id};
        window.appTitle  = {$name};
        window.appPerms  = {$perms};
        window.appOrigin = {$origin};
    </script>

    {script "js/al_games.js"}
{/block}
