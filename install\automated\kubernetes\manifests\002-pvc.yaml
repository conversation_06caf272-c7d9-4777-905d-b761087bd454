apiVersion: v1
items:
- apiVersion: v1
  kind: PersistentVolumeClaim
  metadata:
    name: mariadb-primary-pvc
    namespace: openvk
    annotations: {}
  spec:
    accessModes:
    - ReadWriteOnce
    resources:
      requests:
        storage: 10Gi
- apiVersion: v1
  kind: PersistentVolumeClaim
  metadata:
    name: mariadb-eventdb-pvc
    namespace: openvk
    annotations: {}
  spec:
    accessModes:
    - ReadWriteOnce
    resources:
      requests:
        storage: 10Gi
- apiVersion: v1
  kind: PersistentVolumeClaim
  metadata:
    name: openvk-storage-pvc
    namespace: openvk
    annotations: {}
  spec:
    accessModes:
    - ReadWriteOnce
    resources:
      requests:
        storage: 50Gi
- apiVersion: v1
  kind: PersistentVolumeClaim
  metadata:
    name: openvk-storage-audios-pvc
    namespace: openvk
    annotations: {}
  spec:
    accessModes:
    - ReadWriteOnce
    resources:
      requests:
        storage: 50Gi
- apiVersion: v1
  kind: PersistentVolumeClaim
  metadata:
    name: openvk-storage-photos-pvc
    namespace: openvk
    annotations: {}
  spec:
    accessModes:
    - ReadWriteOnce
    resources:
      requests:
        storage: 50Gi
- apiVersion: v1
  kind: PersistentVolumeClaim
  metadata:
    name: openvk-storage-videos-pvc
    namespace: openvk
    annotations: {}
  spec:
    accessModes:
    - ReadWriteOnce
    resources:
      requests:
        storage: 150Gi
kind: List
metadata: {}