{extends "@layout.xml"}

{block title}
    {_admin_giftsets}
{/block}

{block headingWrap}
    <a style="float: right;" class="aui-button aui-button-primary" href="/admin/gifts/new.0.meta">
        {_create}
    </a>
    
    <h1>{_admin_giftsets}</h1>
{/block}

{block content}
    <div id="spacer" style="height: 8px;"></div>
    {if sizeof($categories) > 0}
        <table class="aui aui-table-list">
            <tbody>
                <tr n:foreach="$categories as $cat">
                    <td style="vertical-align: middle;">
                        <span class="aui-icon aui-icon-small aui-iconfont-folder-filled">{$cat->getName()}</span>
                        {$cat->getName()}
                    </td>
                    <td style="vertical-align: middle;">
                        {ovk_proc_strtr($cat->getDescription(), 128)}
                    </td>
                    <td style="vertical-align: middle; text-align: right;">
                        <a class="aui-button aui-button-primary" href="/admin/gifts/{$cat->getSlug()}.{$cat->getId()}.meta">
                            <span class="aui-icon aui-icon-small aui-iconfont-new-edit">{_edit}</span>
                        </a>
                        
                        <a class="aui-button" href="/admin/gifts/{$cat->getSlug()}.{$cat->getId()}/">
                            <span class="aui-icon aui-icon-small aui-iconfont-gallery">{_admin_open}</span>
                        </a>
                    </td>
                </tr>
            </tbody>
        </table>
    {else}
        <center>
            <p>{_admin_giftsets_none}</p>
        </center>
    {/if}

    <div align="right">
        {var $isLast = ((20 * (($_GET['p'] ?? 1) - 1)) + sizeof($categories)) < $count}
        <a n:if="($_GET['p'] ?? 1) > 1" class="aui-button" href="?act={$act}&p={($_GET['p'] ?? 1) - 1}">&laquo;</a>
        <a n:if="$isLast" class="aui-button" href="?act={$act}&p={($_GET['p'] ?? 1) + 1}">&raquo;</a>
    </div>
{/block}
