{extends "../@layout.xml"}

{block title}{_video}{/block}

{block header}
    <a href="{$user->getURL()}">{$user->getCanonicalName()}</a>
    »
    <a href="/videos{$user->getId()}">{_videos}</a>
    »
    {_video}
{/block}

{block content}
    <div class='media-page-wrapper video-page-wrapper'>
        <div class='video-page-wrapper-video'>
            {if $video->getType() === 0}
                <div class="bsdn" data-name="{$video->getName()}" data-author="{$user->getCanonicalName()}">
                    <video src="{$video->getURL()}"></video>
                </div>
            {else}
                {var $driver = $video->getVideoDriver()}
                {if !$driver}
                    {_unknown_video}
                {else}
                    {$driver->getEmbed()|noescape}
                {/if}
            {/if}
        </div>

        <div class='ovk-vid-details'>
            <div class='media-page-wrapper-description'>
                <p><b>{$video->getName()}</b></p>
                <p n:if='!empty($video->getDescription())'>{$video->getDescription()}</p>
                <div class='upload_time'>
                    {_info_upload_date}: {$video->getPublicationTime()}
                    {if isset($thisUser)}
                        |
                        {var $liked = $video->hasLikeFrom($thisUser)}
                        {var $likesCount = $video->getLikesCount()}
                        <div class='like_wrap tidy'>
                            <a href="/video{$video->getPrettyId()}/like?hash={rawurlencode($csrfToken)}" class="post-like-button" data-liked="{(int) $liked}" data-likes="{$likesCount}" data-id="{$video->getPrettyId()}" data-type='video'>
                                <div class="heart" id="{if $liked}liked{/if}"></div>
                                <span class="likeCnt">{if $likesCount > 0}{$likesCount}{/if}</span>
                            </a>
                        </div>
                    {/if}
                </div>
            </div>

            <hr/>
        
            <div class="media-page-wrapper-details">
                <div class='media-page-wrapper-comments' id="comments">
                    {include "../components/comments.xml",
                            comments => $comments,
                            count => $cCount,
                            page => $cPage,
                            model => "videos",
                            parent => $video}
                </div>
                <div class='media-page-wrapper-actions'>
                    <a href="{$user->getURL()}" class='media-page-author-block'>
                        <img class='cCompactAvatars' src="{$user->getAvatarURL('miniscule')}">

                        <div class='media-page-author-block-name'>
                            <b>{$user->getCanonicalName()}</b>
                        </div>
                    </a>

                    <div>
                        <div n:if="isset($thisUser) && $thisUser->getId() === $user->getId()">
                            <a href="/video{$video->getPrettyId()}/edit" class="profile_link" style="display:block;width:96%;">
                                {_edit}
                            </a>
                            <a id='_videoDelete' href="/video{$video->getPrettyId()}/remove" class="profile_link" style="display:block;width:96%;">
                                {_delete}
                            </a>
                        </div>
                        <a n:if="isset($thisUser)" onclick="javascript:repost({$video->getPrettyId()}, 'video')" class="profile_link" style="display:block;width:96%;">
                            {_share}
                        </a>
                    </div>

                    {if isset($thisUser)}
                        {if $thisUser->getId() != $video->getOwner()->getId()}
                            {var canReport = true}
                        {/if}
                    {/if}

                    <a n:if="$canReport ?? false" class="profile_link" style="display:block;width:96%;" href="javascript:reportVideo({$video->getId()})">{_report}</a>
                    <a n:if="$video->getType() == 0" href="{$video->getURL()}" download="" class="profile_link" style="display:block;width:96%;">{_download_video}</a>
                </div>
            </div>
        </div>
        
        
    </div>
{/block}
