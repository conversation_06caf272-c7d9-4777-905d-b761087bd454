{extends "../@layout.xml"}
{block title}{_my_settings}{/block}

{block header}
    {_my_settings}
{/block}

{block content}

{var $isMain       = $mode === 'main'}
{var $isSecurity   = $mode === 'security'}
{var $isPrivacy    = $mode === 'privacy'}
{var $isFinance    = $mode === 'finance'}
{var $isFinanceTU  = $mode === 'finance.top-up'}
{var $isInterface  = $mode === 'interface'}
{var $isBl         = $mode === 'blacklist'}

       <div class="tabs">
        <div n:attr="id => ($isMain ? 'activetabs' : 'ki')" class="tab">
            <a n:attr="id => ($isMain ? 'act_tab_a' : 'ki')" href="/settings">{_main}</a>
        </div>
        <div n:attr="id => ($isSecurity ? 'activetabs' : 'ki')" class="tab">
            <a n:attr="id => ($isSecurity ? 'act_tab_a' : 'ki')" href="/settings?act=security">{_security}</a>
        </div>
        <div n:attr="id => ($isPrivacy ? 'activetabs' : 'ki')" class="tab">
            <a n:attr="id => ($isPrivacy ? 'act_tab_a' : 'ki')" href="/settings?act=privacy">{_privacy}</a>
        </div>
        <div n:attr="id => ($isBl ? 'activetabs' : 'ki')" class="tab">
            <a n:attr="id => ($isBl ? 'act_tab_a' : 'ki')" href="/settings?act=blacklist">{_blacklist}</a>
        </div>
        <div n:if="OPENVK_ROOT_CONF['openvk']['preferences']['commerce']" n:attr="id => (($isFinance || $isFinanceTU) ? 'activetabs' : 'ki')" class="tab">
            <a n:attr="id => (($isFinance || $isFinanceTU) ? 'act_tab_a' : 'ki')" href="/settings?act=finance">{_points}</a>
        </div>
        <div n:attr="id => ($isInterface ? 'activetabs' : 'ki')" class="tab">
            <a n:attr="id => ($isInterface ? 'act_tab_a' : 'ki')" href="/settings?act=interface">{_interface}</a>
        </div>
    </div>
    
    <div class="container_gray">
        {if $isMain}

        <form action="/settings?act=main" method="POST" enctype="multipart/form-data">
            <h4>{_your_email_address}</h4>
            <table cellspacing="7" cellpadding="0" width="60%" border="0" align="center">
                <tbody>
                    <tr>
                        <td width="120" valign="top">
                            <span class="nobold">{_current_email_address}</span>
                        </td>
                        <td>
                            {$user->getEmail()}
                        </td>
                    </tr>
                    <tr>
                        <td width="120" valign="top">
                            <span class="nobold">{_new_email_address}</span>
                        </td>
                        <td>
                            <input type="email" name="new_email" style="width: 100%;" />
                        </td>
                    </tr>
                    <tr>
                        <td width="120" valign="top">
                            <span class="nobold">{_password}</span>
                        </td>
                        <td>
                            <input type="password" name="email_change_pass" style="width: 100%;" />
                        </td>
                    </tr>
                    <tr n:if="$user->is2faEnabled()">
                        <td width="120" valign="top">
                            <span class="nobold">{_"2fa_code"}</span>
                        </td>
                        <td>
                            <input type="number" autocomplete="off" name="email_change_code" style="width: 100%;" />
                        </td>
                    </tr>
                    <tr>
                        <td>

                        </td>
                        <td>
                            <input type="submit" value="{_save_email_address}" class="button" />
                        </td>
                    </tr>
                </tbody>
            </table>
            <br/>
            <h4>{_your_page_address}</h4>
            <table cellspacing="7" cellpadding="0" width="60%" border="0" align="center">
                <tbody>
                    <tr>
                        <td width="120" valign="top">
                            <span class="nobold">{_page_id}</span>
                        </td>
                        <td>
                            {$user->getId()}
                        </td>
                    </tr>
                    <tr>
                        <td width="120" valign="top">
                            <span class="nobold">{_page_address}</span>
                        </td>
                        <td>
                            <input type="text" name="sc" value="{$user->getShortCode()}" style="width: 100%;" />
                        </td>
                    </tr>
                    <tr>
                        <td>

                        </td>
                        <td>
                            <input type="hidden" name="hash" value="{$csrfToken}" />
                            <input type="submit" value="{_save}" class="button" />
                        </td>
                    </tr>
                </tbody>
            </table>
        </form>

        <br/>
        <div class="settings_delete">
            <a href="/fave">{_bookmarks_tab}</a>
             • 
            <a href="/search?section=posts&from_me=1">{_s_posts}</a>
            <br><br>
            {_you_can_also} <a onClick="showProfileDeactivateDialog({$csrfToken})">{_delete_your_page}</a>.
        </div>

        {elseif $isSecurity}
        <form action="/settings?act=main" method="POST" enctype="multipart/form-data">
            <h4>{_change_password}</h4>
            <table cellspacing="7" cellpadding="0" width="60%" border="0" align="center">
                <tbody>
                    <tr>
                        <td width="120" valign="top">
                            <span class="nobold">{_old_password}</span>
                        </td>
                        <td>
                            <input type="password" name="old_pass" style="width: 100%;" />
                        </td>
                    </tr>
                    <tr>
                        <td width="120" valign="top">
                            <span class="nobold">{_new_password}</span>
                        </td>
                        <td>
                            <input type="password" name="new_pass" style="width: 100%;" />
                        </td>
                    </tr>
                    <tr>
                        <td width="120" valign="top">
                            <span class="nobold">{_repeat_password}</span>
                        </td>
                        <td>
                            <input type="password" name="repeat_pass" style="width: 100%;" />
                        </td>
                    </tr>
                    <tr n:if="$user->is2faEnabled()">
                        <td width="120" valign="top">
                            <span class="nobold">{_"2fa_code"}</span>
                        </td>
                        <td>
                            <input type="number" autocomplete="off" name="password_change_code" style="width: 100%;" />
                        </td>
                    </tr>
                    <tr>
                        <td>

                        </td>
                        <td>
                            <input type="hidden" name="hash" value="{$csrfToken}" />
                            <input type="submit" value="{_change_password}" class="button" />
                        </td>
                    </tr>
                </tbody>
            </table>
            <br/>
        </form>
        <br/>
        <h4>{_two_factor_authentication}</h4>
        <table cellspacing="7" cellpadding="0" width="60%" border="0" align="center">
            <tbody>
                {if $user->is2faEnabled()}
                <tr>
                    <td>
                        <div class="accent-box">
                            {_two_factor_authentication_enabled}
                        </div>
                    </td>
                </tr>
                <tr>
                    <td style="text-align: center;">
                        <a class="button" href="javascript:viewBackupCodes()">{_view_backup_codes}</a>
                        <a class="button" href="javascript:disableTwoFactorAuth()">{_disable}</a>
                    </td>
                </tr>

                <script>
                    function viewBackupCodes() {
                        MessageBox(tr("viewing_backup_codes"), `
                            <form id="back-codes-view-form" method="post" action="/settings/2fa">
                                <label for="password">Пароль</label>
                                <input type="password" id="password" name="password" required />
                                <input type="hidden" name="hash" value={$csrfToken} />
                            </form>
                            `, [tr("viewing"), tr("cancel")], [
                            () => {
                                document.querySelector("#back-codes-view-form").submit();
                            }, Function.noop
                        ]);
                    }

                    function disableTwoFactorAuth() {
                        MessageBox(tr("disable_2fa"), `
                            <form id="two-factor-auth-disable-form" method="post" action="/settings/2fa/disable">
                                <label for="password">Пароль</label>
                                <input type="password" id="password" name="password" required />
                                <input type="hidden" name="hash" value={$csrfToken} />
                            </form>
                            `, [tr("disable"), tr("cancel")], [
                            () => {
                                document.querySelector("#two-factor-auth-disable-form").submit();
                            }, Function.noop
                        ]);
                    }
                </script>
                {else}
                <tr>
                    <td>
                        <div class="accent-box">
                            {_two_factor_authentication_disabled}
                        </div>
                    </td>
                </tr>
                <tr>
                    <td style="text-align: center;">
                        <a class="button" href="/settings/2fa">{_connect}</a>
                    </td>
                </tr>
                {/if}
            </tbody>
        </table>
        <h4>{_ui_settings_sessions}</h4>
        <form action="/revokeAllTokens" method="POST">
            <table cellspacing="7" cellpadding="0" width="60%" border="0" align="center">
                <tbody>
                    <tr>
                        <td>
                            <div class="accent-box">
                                {tr("end_all_sessions_description", OPENVK_ROOT_CONF['openvk']['appearance']['name'])}
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td width="120" valign="top" style="text-align: center;">
                            <input type="hidden" name="hash" value="{$csrfToken}" />
                            <input type="submit" value="{_end_all_sessions}" class="button" />
                        </td>
                    </tr>
                </tbody>
            </table>
        </form>
        
        {elseif $isPrivacy}

        <form action="/settings?act=privacy" method="POST" enctype="multipart/form-data">
            <table cellspacing="7" cellpadding="0" width="60%" border="0" align="center">
                <tr>
                    <td width="120" valign="top">
                        <span class="nobold">{_privacy_setting_access_page}</span>
                    </td>
                    <td>
                        <select name="page.read" style="width: 164px;">
                            <option value="3" {if $user->getPrivacySetting('page.read') == 3}selected{/if}>{_privacy_value_anybody_dative}</option>
                            <option value="2" {if $user->getPrivacySetting('page.read') == 2}selected{/if}>{_privacy_value_users}</option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <td width="120" valign="top">
                        <span class="nobold">{_privacy_setting_read_info}</span>
                    </td>
                    <td>
                        <select name="page.info.read" style="width: 164px;">
                            <option value="3" {if $user->getPrivacySetting('page.info.read') == 3}selected{/if}>{_privacy_value_anybody_dative}</option>
                            <option value="2" {if $user->getPrivacySetting('page.info.read') == 2}selected{/if}>{_privacy_value_users}</option>
                            <option value="1" {if $user->getPrivacySetting('page.info.read') == 1}selected{/if}>{_privacy_value_friends_dative}</option>
                            <option value="0" {if $user->getPrivacySetting('page.info.read') == 0}selected{/if}>{_privacy_value_only_me_dative}</option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <td width="120" valign="top">
                        <span class="nobold">{_privacy_setting_see_groups}</span>
                    </td>
                    <td>
                        <select name="groups.read" style="width: 164px;">
                            <option value="3" {if $user->getPrivacySetting('groups.read') == 3}selected{/if}>{_privacy_value_anybody_dative}</option>
                            <option value="2" {if $user->getPrivacySetting('groups.read') == 2}selected{/if}>{_privacy_value_users}</option>
                            <option value="1" {if $user->getPrivacySetting('groups.read') == 1}selected{/if}>{_privacy_value_friends_dative}</option>
                            <option value="0" {if $user->getPrivacySetting('groups.read') == 0}selected{/if}>{_privacy_value_only_me_dative}</option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <td width="120" valign="top">
                        <span class="nobold">{_privacy_setting_see_photos}</span>
                    </td>
                    <td>
                        <select name="photos.read" style="width: 164px;">
                            <option value="3" {if $user->getPrivacySetting('photos.read') == 3}selected{/if}>{_privacy_value_anybody_dative}</option>
                            <option value="2" {if $user->getPrivacySetting('photos.read') == 2}selected{/if}>{_privacy_value_users}</option>
                            <option value="1" {if $user->getPrivacySetting('photos.read') == 1}selected{/if}>{_privacy_value_friends_dative}</option>
                            <option value="0" {if $user->getPrivacySetting('photos.read') == 0}selected{/if}>{_privacy_value_only_me_dative}</option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <td width="120" valign="top">
                        <span class="nobold">{_privacy_setting_see_videos}</span>
                    </td>
                    <td>
                        <select name="videos.read" style="width: 164px;">
                            <option value="3" {if $user->getPrivacySetting('videos.read') == 3}selected{/if}>{_privacy_value_anybody_dative}</option>
                            <option value="2" {if $user->getPrivacySetting('videos.read') == 2}selected{/if}>{_privacy_value_users}</option>
                            <option value="1" {if $user->getPrivacySetting('videos.read') == 1}selected{/if}>{_privacy_value_friends_dative}</option>
                            <option value="0" {if $user->getPrivacySetting('videos.read') == 0}selected{/if}>{_privacy_value_only_me_dative}</option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <td width="120" valign="top">
                        <span class="nobold">{_privacy_setting_view_audio}</span>
                    </td>
                    <td>
                        <select name="audios.read" style="width: 164px;">
                            <option value="3" {if $user->getPrivacySetting('audios.read') == 3}selected{/if}>{_privacy_value_anybody_dative}</option>
                            <option value="2" {if $user->getPrivacySetting('audios.read') == 2}selected{/if}>{_privacy_value_users}</option>
                            <option value="1" {if $user->getPrivacySetting('audios.read') == 1}selected{/if}>{_privacy_value_friends_dative}</option>
                            <option value="0" {if $user->getPrivacySetting('audios.read') == 0}selected{/if}>{_privacy_value_only_me_dative}</option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <td width="120" valign="top">
                        <span class="nobold">{_privacy_setting_see_notes}</span>
                    </td>
                    <td>
                        <select name="notes.read" style="width: 164px;">
                            <option value="3" {if $user->getPrivacySetting('notes.read') == 3}selected{/if}>{_privacy_value_anybody_dative}</option>
                            <option value="2" {if $user->getPrivacySetting('notes.read') == 2}selected{/if}>{_privacy_value_users}</option>
                            <option value="1" {if $user->getPrivacySetting('notes.read') == 1}selected{/if}>{_privacy_value_friends_dative}</option>
                            <option value="0" {if $user->getPrivacySetting('notes.read') == 0}selected{/if}>{_privacy_value_only_me_dative}</option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <td width="120" valign="top">
                        <span class="nobold">{_privacy_setting_see_friends}</span>
                    </td>
                    <td>
                        <select name="friends.read" style="width: 164px;">
                            <option value="3" {if $user->getPrivacySetting('friends.read') == 3}selected{/if}>{_privacy_value_anybody_dative}</option>
                            <option value="2" {if $user->getPrivacySetting('friends.read') == 2}selected{/if}>{_privacy_value_users}</option>
                            <option value="1" {if $user->getPrivacySetting('friends.read') == 1}selected{/if}>{_privacy_value_friends_dative}</option>
                            <option value="0" {if $user->getPrivacySetting('friends.read') == 0}selected{/if}>{_privacy_value_only_me_dative}</option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <td width="120" valign="top">
                        <span class="nobold">{_privacy_setting_add_to_friends}</span>
                    </td>
                    <td>
                        <select name="friends.add" style="width: 164px;">
                            <option value="3" {if $user->getPrivacySetting('friends.add') == 2}selected{/if}>{_privacy_value_anybody}</option>
                            <option value="0" {if $user->getPrivacySetting('friends.add') == 0}selected{/if}>{_privacy_value_nobody}</option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <td width="120" valign="top">
                        <span class="nobold">{_privacy_setting_write_wall}</span>
                    </td>
                    <td>
                        <select name="wall.write" style="width: 164px;">
                            <option value="2" {if $user->getPrivacySetting('wall.write') == 2}selected{/if}>{_privacy_value_anybody}</option>
                            <option value="1" {if $user->getPrivacySetting('wall.write') == 1}selected{/if}>{_privacy_value_friends}</option>
                            <option value="0" {if $user->getPrivacySetting('wall.write') == 0}selected{/if}>{_privacy_value_only_me}</option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <td width="120" valign="top">
                        <span class="nobold">{_privacy_setting_write_messages}</span>
                    </td>
                    <td>
                        <select name="messages.write", style="width: 164px;">
                            <option value="2" {if $user->getPrivacySetting('messages.write') == 2}selected{/if}>{_privacy_value_anybody}</option>
                            <option value="1" {if $user->getPrivacySetting('messages.write') == 1}selected{/if}>{_privacy_value_friends}</option>
                            <option value="0" {if $user->getPrivacySetting('messages.write') == 0}selected{/if}>{_privacy_value_nobody}</option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <td width="120" valign="top">
                        <span class="nobold">{_privacy_setting_see_likes}</span>
                    </td>
                    <td>
                        <select name="likes.read", style="width: 164px;">
                            <option value="2" {if $user->getPrivacySetting('likes.read') == 2}selected{/if}>{_privacy_value_anybody_dative}</option>
                            <option value="0" {if $user->getPrivacySetting('likes.read') == 0}selected{/if}>{_privacy_value_only_me_dative}</option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <td width="120" valign="top">
                        <span class="nobold">{_profile_type}</span>
                    </td>
                    <td>
                        <select name="profile_type", style="width: 164px;">
                            <option value="0" {if $user->getProfileType() == 0}selected{/if}>{_profile_type_open}</option>
                            <option value="1" {if $user->getProfileType() == 1}selected{/if}>{_profile_type_closed}</option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <td>
                        
                    </td>
                    <td>
                        <input type="hidden" name="hash" value="{$csrfToken}" />
                        <input type="submit" value="{_save}" class="button" />
                    </td>
                </tr>
            </table>
        </form>
        
        {elseif $isFinance}
        
        <div style="width: 75%; display: inline-block;">
            {presenter "openvk!Support->knowledgeBaseArticle", "points"}
            <center>{tr("also_you_can_transfer_points", $thisUser->getCoins(), rawurlencode($csrfToken))|noescape}</center>
        </div>
        <div style="width: 22%; float: right;">
            <div style="margin: 0; font-size: medium; text-align: center; font-weight: 900;">
                {_on_your_account}
                <div style="width: 133px; height: 60px; font-weight: 100; white-space: nowrap; position: relative;" id="balance">
                    <span class="textFitted textFitAlignVert" style="display: inline-block; font-size: 48px; height: 60px;">{$thisUser->getCoins()}</span>
                </div>
                {_points_count}<br/>
                <small><a href="?act=finance.top-up">[{_have_voucher}?]</a></small><br>
                <small n:if="OPENVK_ROOT_CONF['openvk']['preferences']['ton']['enabled']">
                    <a href="javascript:showCoinsTopUpTroughTon()">[{_transfer_trough_ton}]</a>
                </small>
            </div>
        </div>
        
        {script "js/node_modules/textfit/textFit.min.js"}
        <script>
            let balance = document.querySelector("#balance");
            
            balance.style.width = Math.ceil(balance.parentNode.getBoundingClientRect().width) + "px";
            textFit(balance, { alignVert: true });

            function convertCurrency(coin) {
                var currencyTON = {php echo OPENVK_ROOT_CONF['openvk']['preferences']['ton']['rate']};
                u(".coins_dialog_conv").nodes[0].innerHTML = coin * currencyTON + " TON"; 
            }

            function showCoinsTopUpTroughTon() {
                MessageBox(tr("transfer_trough_ton"), `
                    <div class="messagebox-content-header">
                        ${ tr("transfer_ton_contents") }
                    </div>
                    <div style="margin-top: 30px">` +
                    {tr("transfer_ton_address", OPENVK_ROOT_CONF['openvk']['preferences']['ton']['address'], str_replace("$1", $thisUser->getId(), OPENVK_ROOT_CONF["openvk"]["preferences"]["ton"]["hint"]))} + 
                    `<div style="text-align: center;">
                        <img width="225" height="225" src="data:` + {$qrCodeType} + `;base64,` + {$qrCodeData} + `"><br>
                        <input oninput="convertCurrency(this.value)"> = <span class="coins_dialog_conv">0 TON</span>
                    </div>
                    </div>`
                , [tr("close")], [
                    Function.noop
                ]);
            }
        </script>
        
        {elseif $isFinanceTU}
        
        <p>{_voucher_explanation} {_voucher_explanation_ex}</p>
        <form name="vouncher_form" action="/settings?act=finance.top-up" method="POST" enctype="multipart/form-data">
            <input type="text" name="key0" class="vouncher_input" size="6" maxlength="6" placeholder="123456" required="required" oninput="autoTab(this, document.vouncher_form.key1, undefined)" style="display: inline-block; width: 50px; text-align: center;" /> -
            <input type="text" name="key1" class="vouncher_input" size="6" maxlength="6" placeholder="789012" required="required" oninput="autoTab(this, document.vouncher_form.key2, document.vouncher_form.key0)" style="display: inline-block; width: 50px; text-align: center;" /> -
            <input type="text" name="key2" class="vouncher_input" size="6" maxlength="6" placeholder="345678" required="required" oninput="autoTab(this, document.vouncher_form.key3, document.vouncher_form.key1)" style="display: inline-block; width: 50px; text-align: center;" /> -
            <input type="text" name="key3" class="vouncher_input" size="6" maxlength="6" placeholder="90ABCD" required="required" oninput="autoTab(this, undefined, document.vouncher_form.key2)" style="display: inline-block; width: 50px; text-align: center;" />
            <br/><br/>
            
            <input type="hidden" name="hash" value="{$csrfToken}" />
            <input type="submit" value="{_redeem}" class="button" />
        </form>
        
        {elseif $isInterface}
        
        <h4>{_ui_settings_interface}</h4>
        <form action="/settings?act=interface" method="POST" enctype="multipart/form-data">
            <table cellspacing="7" cellpadding="0" width="60%" border="0" align="center">
                <tbody>
                    <tr>
                        <td width="120" valign="top">
                            <span class="nobold">{_avatars_style}</span>
                        </td>
                        <td>
                            <select name="style_avatar">
                                <option value="0" {if $user->getStyleAvatar() == 0}selected{/if}>{_arbitrary_avatars} ({_default})</option>
                                <option value="1" {if $user->getStyleAvatar() == 1}selected{/if}>{_cut}</option>
                                <option value="2" {if $user->getStyleAvatar() == 2}selected{/if}>{_round_avatars}</option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <td width="120" valign="top">
                            <span class="nobold">{_style}</span>
                        </td>
                        <td>
                            <select name="style">
                                <option value="ovk" {if $user->getStyle() == 'ovk'}selected{/if}>OpenVK ({_default})</option>
                                <option n:foreach="$themes as $id => $theme"
                                        n:attr="selected => $user->getStyle() === $id"
                                        value="{$id}">
                                            {$theme}
                                </option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <td width="120" valign="top">
                            <span class="nobold">{_ui_settings_rating}</span>
                        </td>
                        <td>
                            <select name="rating">
                                <option value="1" {if !$user->prefersNotToSeeRating()}selected{/if}>{_ui_settings_rating_show}</option>
                                <option value="0" {if $user->prefersNotToSeeRating()}selected{/if}>{_ui_settings_rating_hide}</option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <td width="120" valign="top">
                            <span class="nobold">{_ui_settings_nsfw_content}</span>
                        </td>
                        <td>
                            <select name="nsfw">
                                <option value="0" {if $user->getNsfwTolerance() === 0}selected{/if}>{_ui_settings_nsfw_content_dont_show}</option>
                                <option value="1" {if $user->getNsfwTolerance() === 1}selected{/if}>{_ui_settings_nsfw_content_blur}</option>
                                <option value="2" {if $user->getNsfwTolerance() === 2}selected{/if}>{_ui_settings_nsfw_content_show}</option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <td width="120" valign="top">
                            <span class="nobold">{_ui_settings_view_of_posts}</span>
                        </td>
                        <td>
                            <select name="microblog">
                                <option value="0" {if !$user->hasMicroblogEnabled()}selected{/if}>{_ui_settings_view_of_posts_old}</option>
                                <option value="1" {if $user->hasMicroblogEnabled()}selected{/if}>{_ui_settings_view_of_posts_microblog}</option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <td width="120" valign="top">
                            <span class="nobold">{_ui_settings_main_page}</span>
                        </td>
                        <td>
                            <select name="main_page">
                                <option value="0" {if !$user->getMainPage()}selected{/if}>{_my_page}</option>
                                <option value="1" {if $user->getMainPage()}selected{/if}>{_my_feed}</option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <td width="120" valign="top" align="right">
                            <input type="checkbox" name="theme_for_session" value="1">
                        </td>
                        <td>
                            {_apply_style_for_this_device}
                        </td>
                    </tr>
                    <tr>
                        <td>
                            
                        </td>
                        <td>
                            <input type="hidden" name="hash" value="{$csrfToken}" />
                            <input type="submit" value="{_save}" class="button" />
                        </td>
                    </tr>
                </tbody>
            </table>
        </form>

        <table cellspacing="7" cellpadding="0" width="60%" border="0" align="center" id="_js_settings">
            <tbody>
                <tr>
                    <td width="120" valign="top" align="right"></td>
                    <td>
                        <a href="javascript:openJsSettings()">{_ui_settings_window}</a>
                    </td>
                </tr>
            </tbody>
        </table>
        
        <h4>{_ui_settings_sidebar}</h4>
        <form action="/settings?act=lMenu" method="POST" enctype="multipart/form-data">
            <table cellspacing="7" cellpadding="0" width="60%" border="0" align="center">
                <tbody>
                    <tr>
                        <td width="120" valign="top" align="right">
                            <input
                                  n:attr="checked => $user->getLeftMenuItemStatus('photos')"
                                  type="checkbox"
                                  name="menu_bildoj" />
                        </td>
                        <td>
                            <span class="nobold">{_my_photos}</span>
                        </td>
                    </tr>
                    <tr>
                        <td width="120" valign="top" align="right">
                            <input
                                  n:attr="checked => $user->getLeftMenuItemStatus('videos')"
                                  type="checkbox"
                                  name="menu_filmetoj" />
                        </td>
                        <td>
                            <span class="nobold">{_my_videos}</span>
                        </td>
                    </tr>
                    <tr>
                        <td width="120" valign="top" align="right">
                            <input
                                  n:attr="checked => $user->getLeftMenuItemStatus('audios')"
                                  type="checkbox"
                                  name="menu_muziko" />
                        </td>
                        <td>
                            <span class="nobold">{_my_audios}</span>
                        </td>
                    </tr>
                    <tr>
                        <td width="120" valign="top" align="right">
                            <input
                                  n:attr="checked => $user->getLeftMenuItemStatus('messages')"
                                  type="checkbox"
                                  name="menu_mesagoj" />
                        </td>
                        <td>
                            <span class="nobold">{_my_messages}</span>
                        </td>
                    </tr>
                    <tr>
                        <td width="120" valign="top" align="right">
                            <input
                                  n:attr="checked => $user->getLeftMenuItemStatus('notes')"
                                  type="checkbox"
                                  name="menu_notatoj" />
                        </td>
                        <td>
                            <span class="nobold">{_my_notes}</span>
                        </td>
                    </tr><tr>
                        <td width="120" valign="top" align="right">
                            <input
                                  n:attr="checked => $user->getLeftMenuItemStatus('groups')"
                                  type="checkbox"
                                  name="menu_grupoj" />
                        </td>
                        <td>
                            <span class="nobold">{_my_groups}</span>
                        </td>
                    </tr><tr>
                        <td width="120" valign="top" align="right" align="right">
                            <input
                                  n:attr="checked => $user->getLeftMenuItemStatus('news')"
                                  type="checkbox"
                                  name="menu_novajoj" />
                        </td>
                        <td>
                            <span class="nobold">{_my_feed}</span>
                        </td>
                    </tr><tr>
                        <td width="120" valign="top" align="right" align="right">
                            <input
                                  n:attr="checked => $user->getLeftMenuItemStatus('apps')"
                                  type="checkbox"
                                  name="menu_aplikoj" />
                        </td>
                        <td>
                            <span class="nobold">{_my_apps}</span>
                        </td>
                    </tr>
                    <tr>
                        <td width="120" valign="top" align="right" align="right">
                            <input
                                  n:attr="checked => $user->getLeftMenuItemStatus('docs')"
                                  type="checkbox"
                                  name="menu_doxc" />
                        </td>
                        <td>
                            <span class="nobold">{_my_documents}</span>
                        </td>
                    </tr>
                    <tr style="display:none;">
                        <td width="120" valign="top" align="right" align="right">
                            <input
                                  n:attr="checked => $user->getLeftMenuItemStatus('fave')"
                                  type="checkbox"
                                  name="menu_feva" />
                        </td>
                        <td>
                            <span class="nobold">{_bookmarks_tab}</span>
                        </td>
                    </tr>
                    <tr n:if="sizeof(OPENVK_ROOT_CONF['openvk']['preferences']['menu']['links']) > 0">
                        <td width="120" valign="top" align="right" align="right">
                            <input
                                  n:attr="checked => $user->getLeftMenuItemStatus('links')"
                                  type="checkbox"
                                  name="menu_ligiloj" />
                        </td>
                        <td>
                            <span class="nobold">{_additional_links}</span>
                        </td>
                    </tr><tr n:if="OPENVK_ROOT_CONF['openvk']['preferences']['adPoster']['enable']">
                        <td width="120" valign="top" align="right" align="right">
                            <input
                                  n:attr="checked => $user->getLeftMenuItemStatus('poster')"
                                  type="checkbox"
                                  name="menu_standardo" />
                        </td>
                        <td>
                            <span class="nobold">{_ad_poster}</span>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            
                        </td>
                        <td>
                            <input type="hidden" name="hash" value="{$csrfToken}" />
                            <input type="submit" value="{_save}" class="button" />
                        </td>
                    </tr>
                </tbody>
            </table>
        </form>
        {elseif $isBl}
            {if $blSize < 1}
                {include "../components/error.xml", description => tr("bl_count_zero_desc")}
            {else}
                <h4 style="margin-bottom: 10px;">{tr("bl_count", $blSize)}.</h4>
                <div class='entity_vertical_list mini m_mini scroll_container'>
                    <div n:foreach="$blItems as $item" class="entity_vertical_list_item scroll_node">
                        <div class="first_column">
                            <a href="{$item->getURL()}" class="avatar">
                                <img src='{$item->getAvatarURL()}'>
                            </a>
                            <div class="info">
                                <b class="noOverflow">
                                    <a href="{$item->getURL()}">
                                        {$item->getCanonicalName()} 
                                    </a>
                                </b>
                            </div>
                        </div>
                    </div>
                </div>
                {include "../components/paginator.xml", conf => $paginatorConf}
            {/if}
        {/if}
    </div>
    
{/block}
