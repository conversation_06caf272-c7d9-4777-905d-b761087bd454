{extends "@layout.xml"}

{block title}
    {_admin_banned_links}
{/block}

{block heading}
    <a style="float: right;" class="aui-button aui-button-primary" href="/admin/bannedLink/id0">
        {_create}
    </a>

    {_admin_banned_links}
{/block}

{block content}
    <table class="aui aui-table-list">
        <thead>
            <tr>
                <th>ID</th>
                <th>{_admin_banned_domain}</th>
                <th>REGEXP</th>
                <th>{_admin_banned_link_reason}</th>
                <th>{_admin_banned_link_initiator}</th>
                <th>{_admin_actions}</th>
            </tr>
        </thead>
        <tbody>
            <tr n:foreach="$links as $link">
                <td>{$link->getId()}</td>
                <td>{$link->getDomain()}</td>
                <td>{$link->getRegexpRule()}</td>
                <td>{$link->getReason() ?? "-"}</td>
                <td>{$link->getInitiator()->getCanonicalName()}</td>
                <td>
                    <a class="aui-button aui-button-primary" href="/admin/bannedLink/id{$link->getId()}">
                        <span class="aui-icon aui-icon-small aui-iconfont-new-edit">{_edit}</span>
                    </a>
                </td>
            </tr>
        </tbody>
    </table>
    <div align="right">
        <a n:if="($_GET['p'] ?? 1) > 1" class="aui-button" href="?p={($_GET['p'] ?? 1) - 1}">«</a>
        <a class="aui-button" href="?p={($_GET['p'] ?? 1) + 1}">»</a>
    </div>
{/block}