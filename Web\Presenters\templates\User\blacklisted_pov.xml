{extends "../@layout.xml"}
{block title}{$user->getCanonicalName()}{/block}

{block header}
    {$user->getCanonicalName()}
    <img n:if="$user->isVerified()"
         class="name-checkmark"
         src="/assets/packages/static/openvk/img/checkmark.png"
         />
{/block}

{block content}
    <div class="left_small_block">
        <div>
            <img src="{$user->getAvatarUrl('normal')}"
                alt="{$user->getCanonicalName()}"
                style="width: 100%; image-rendering: -webkit-optimize-contrast;" />
        </div>
        <div id="profile_links" n:if="isset($thisUser)">
            <a n:if="!$blacklist_status" id="_bl_toggler" data-val="0" data-id="{$user->getRealId()}" class="profile_link" style="display:block;width:96%;">{_bl_remove}</a>
            <a n:if="!$user->isHideFromGlobalFeedEnabled()" class="profile_link" style="display:block;width:96%;" id="__ignoreSomeone" data-val='{!$ignore_status ? 1 : 0}' data-id="{$user->getId()}">
                {if !$ignore_status}{_ignore_user}{else}{_unignore_user}{/if}
            </a>
            <a class="profile_link" style="display:block;width:96%;" href="javascript:reportUser({$user->getId()})">{_report}</a>
        </div>
    </div>

    <div class="right_big_block">
        <div class="page_info">
            <div class="accountInfo clearFix">
                <div class="profileName">
                    <h2>{$user->getFullName()}</h2>
                </div>
            </div>
            <div class="msg msg_yellow" style="width: 93%;margin-top: 10px;">
                {tr("you_blacklisted", $user->getMorphedName("genitive", false))}.
            </div>
        </div>
    </div>
{/block}
