{extends "../@layout.xml"}

{block title}
    {tr("search_for_$section")}
    {if $_REQUEST['q']}
        - {$_REQUEST['q']}
    {/if}
{/block}

{block header}
    {tr("search_for_$section")}
{/block}

{block wrap}
    <div class="wrap2">
        <div class="wrap1">
            <div class="page_wrap">
                {if $section == 'audios' && $count > 1}
                    {include "../Audio/bigplayer.xml", tidy => true}
                    <script>
                        window.__current_page_audio_context = {
                            'name': 'classic_search_context',
                            'order': {$order},
                            'query': {$query},
                            'genre': {$_REQUEST['genre']},
                            'invert': {$invert ? 1 : 0},
                            'only_performers': {$_REQUEST['only_performers'] ? 1 : 0},
                            'with_lyrics': {$_REQUEST['with_lyrics'] ? 1 : 0},
                            'page': {$page}
                        }
                    </script>
                {/if}
                <div class='summaryBar summaryBarFlex padding'>
                    <div class='summary'>
                        <b>{tr("results", $count)} {tr("showing_x_y", $page, $count)}</b>
                    </div>

                    {include "../components/paginator.xml", conf => $paginatorConf}
                </div>

                <div class='page_wrap_content' id='search_page'>
                    <div n:class='page_wrap_content_main, scroll_container, $section == "audios" && $count > 0 ? audios_padding'>
                        {if $count > 0}
                            {if $section === 'users'}
                                <div class='scroll_node search_content content def_row_content' n:foreach="$data as $dat">
                                    <table>
                                        <tbody>
                                            <tr>
                                                <td valign="top">
                                                    <a href="{$dat->getURL()}">
                                                        <img src="{$dat->getAvatarUrl('tiny')}" width="75" alt="{_photo}" loading='lazy' />
                                                    </a>
                                                </td>
                                                <td valign="top" style="width: 100%">
                                                    <a href="{$dat->getURL()}">
                                                        <b>
                                                            <text style="overflow: hidden;">&nbsp;{$dat->getCanonicalName()}  
                                                                {if $order     == "rating"}
                                                                    ({$dat->getProfileCompletenessReport()->total}%)
                                                                {/if}
                                                            </text>
                                                            <img n:if="$dat->isVerified()" 
                                                                class="name-checkmark"
                                                                src="/assets/packages/static/openvk/img/checkmark.png"
                                                            />
                                                        </b>
                                                        {if $dat->getId() == $thisUser->getId()}
                                                            ({_s_it_is_you})
                                                        {/if}
                                                    </a>

                                                    <table class="ugc-table">
                                                        <tbody>
                                                            {if $_REQUEST["order"] == "id"}
                                                                <tr>
                                                                    <td><span class="nobold">ID:</span></td>
                                                                    <td>{$dat->getId()}</td>
                                                                </tr>
                                                            {/if}
                                                            {if $dat->getPrivacySetting("page.info.read") > 1}        
                                                                <tr>
                                                                    <td><span class="nobold">{_pronouns}: </span></td>
                                                                    <td>{$dat->isFemale() ? tr("female") : ($dat->isNeutral() ? tr("neutral") : tr("male"))}</td>
                                                                </tr>
                                                                <tr>
                                                                    <td><span class="nobold">{_relationship}:</span></td>
                                                                    <td>{$dat->getLocalizedMaritalStatus()}</td>
                                                                </tr>
                                                                <tr>
                                                                    <td><span class="nobold">{_registration_date}: </span></td>
                                                                    <td>{$dat->getRegistrationTime()}</td>
                                                                </tr>
                                                                {if !empty($dat->getDescription())}
                                                                <tr>
                                                                    <td>
                                                                        <span class="nobold">{_description}:</span>
                                                                    </td>
                                                                    <td>
                                                                        {$dat->getDescription() ?? '(' . tr("none") . ')'}
                                                                    </td>
                                                                </tr>
                                                                {/if}
                                                                {if !empty($_REQUEST["fav_mus"])}
                                                                    <tr>
                                                                        <td><span class="nobold">{_favorite_music}:</span></td>
                                                                        <td>{$dat->getFavoriteMusic()}</td>
                                                                    </tr>
                                                                {/if}
                                                                {if !empty($_REQUEST["fav_films"])}
                                                                    <tr>
                                                                        <td><span class="nobold">{_favorite_films}:</span></td>
                                                                        <td>{$dat->getFavoriteFilms()}</td>
                                                                    </tr>
                                                                {/if}
                                                                {if !empty($_REQUEST["fav_shows"])}
                                                                    <tr>
                                                                        <td><span class="nobold">{_favorite_shows}:</span></td>
                                                                        <td>{$dat->getFavoriteShows()}</td>
                                                                    </tr>
                                                                {/if}
                                                                {if !empty($_REQUEST["fav_books"])}
                                                                    <tr>
                                                                        <td><span class="nobold">{_favorite_books}:</span></td>
                                                                        <td>{$dat->getFavoriteBooks()}</td>
                                                                    </tr>
                                                                {/if}
                                                            {/if}
                                                        </tbody>
                                                    </table>
                                                    <br/>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>

                                <script n:if='$count > 0 && !empty($query)'>
                                    function __scrollHook(page) {
                                        highlightText({$query}, '.page_wrap_content_main', ['text'])
                                    }

                                    __scrollHook()
                                </script>
                            {elseif $section === 'groups'}
                                <div class='scroll_node search_content content def_row_content' n:foreach="$data as $dat">
                                    <table>
                                        <tbody>
                                            <tr>
                                                <td valign="top">
                                                    <a href="{$dat->getURL()}">
                                                        <img src="{$dat->getAvatarUrl('tiny')}" width="75" alt="{_photo}" loading='lazy' />
                                                    </a>
                                                </td>
                                                <td valign="top" style="width: 100%">
                                                    <a href="{$dat->getURL()}">
                                                        <b>
                                                            <text style="overflow: hidden;">&nbsp;{$dat->getCanonicalName()}  
                                                                {if $order == "id"}
                                                                    (id{$dat->getId()})
                                                                {/if}
                                                            </text>
                                                            <img n:if="$dat->isVerified()" 
                                                                class="name-checkmark"
                                                                src="/assets/packages/static/openvk/img/checkmark.png"
                                                            />
                                                        </b>
                                                    </a>

                                                    <table class="ugc-table">
                                                        <tbody>
                                                            {if !empty($dat->getDescription())}
                                                                <tr>
                                                                    <td>
                                                                        <span class="nobold">{_description}:</span>
                                                                    </td>
                                                                    <td data-highlight='_clubDesc'>
                                                                        {$dat->getDescription() ?? '(' . tr("none") . ')'}
                                                                    </td>
                                                                </tr>
                                                            {/if}
                                                            <td>
                                                                <span class="nobold">{_size}:</span>
                                                            </td>
                                                            <td>
                                                                {tr("participants", $dat->getFollowersCount())}
                                                            </td>
                                                        </tbody>
                                                    </table>
                                                    <br/>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>

                                <script n:if='$count > 0 && !empty($query)'>
                                    function __scrollHook(page) {
                                        highlightText({$query}, '.page_wrap_content_main', ['text', "td[data-highlight='_clubDesc']"])
                                    }

                                    __scrollHook()
                                </script>
                            {elseif $section === 'apps'}
                                <div class='scroll_node search_content content def_row_content' n:foreach="$data as $dat">
                                    <table>
                                        <tbody>
                                            <tr>
                                                <td valign="top">
                                                    <a href="/app{$dat->getId()}">
                                                        <img src="{$dat->getAvatarUrl('miniscule')}" width="75" alt="{_photo}" loading='lazy' />
                                                    </a>
                                                </td>
                                                <td valign="top" style="width: 100%">
                                                    <a href="/app{$dat->getId()}">
                                                        <b>
                                                            <text style="overflow: hidden;">
                                                                &nbsp;{$dat->getName()}
                                                            </text>
                                                        </b>
                                                    </a><br/>
                                                    <span style='margin-left: 2px;' data-highlight='_appDesc'>
                                                        {$dat->getDescription() ?? '(' . tr("none") . ')'}
                                                    </span>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>

                                <script n:if='$count > 0 && !empty($query)'>
                                    function __scrollHook(page) {
                                        highlightText({$query}, '.page_wrap_content_main', ['text', "span[data-highlight='_appDesc']"])
                                    }

                                    __scrollHook()
                                </script>
                            {elseif $section === 'posts'}
                                <div class='scroll_node search_content' n:foreach="$data as $dat">
                                    {if !$dat || $dat->getWallOwner()->isHideFromGlobalFeedEnabled()}
                                        {_closed_group_post}.
                                    {else}
                                        {include "../components/post.xml", post => $dat, commentSection => true, onWallOf => true}
                                    {/if}
                                </div>

                                <script n:if='$count > 0 && !empty($query)'>
                                    function __scrollHook(page) {
                                        highlightText({$query}, '.page_wrap_content_main', [".post:not(.comment) > tbody > tr > td > .post-content > .text .really_text"])
                                    }

                                    __scrollHook()
                                </script>
                            {elseif $section === 'videos'}
                                <div class='scroll_node search_content' n:foreach="$data as $dat">
                                    {include "../components/video.xml", video => $dat}
                                </div>    

                                <script n:if='$count > 0 && !empty($query)'>
                                    function __scrollHook(page) {
                                        highlightText({$query}, '.page_wrap_content_main', [".video_name", ".video_description"])
                                    }

                                    __scrollHook()
                                </script>
                            {elseif $section === 'audios'}
                                <div class='scroll_node search_content' n:foreach="$data as $dat">
                                    {include "../Audio/player.xml", audio => $dat}
                                </div>
                                
                                <script n:if="$count > 0 && !empty($query) && empty($_REQUEST['only_performers'])">
                                    function __scrollHook(page) {
                                        highlightText({$query}, '.page_wrap_content_main', [".mediaInfo .performer a", ".mediaInfo .title"])
                                    }

                                    __scrollHook()
                                </script>
                            {elseif $section === 'audios_playlists'}
                                <div class='scroll_node search_content' n:foreach="$data as $dat">
                                    {include "../Audio/playlistListView.xml", playlist => $dat}
                                </div>

                                <script n:if="$count > 0 && !empty($query) && empty($_REQUEST['only_performers'])">
                                    function __scrollHook(page) {
                                        highlightText({$query}, '.page_wrap_content_main', [".playlistName", ".playlistDesc"])
                                    }

                                    __scrollHook()
                                </script>
                            {elseif $section === 'docs'}
                                <div class='scroll_node search_content' n:foreach="$data as $dat">
                                    {include "../Documents/components/doc.xml", doc => $dat, copyImportance => true}
                                </div>

                                <script n:if="$count > 0 && !empty($query)">
                                    function __scrollHook(page) {
                                        highlightText({$query}, '.page_wrap_content_main', [".doc_content .noOverflow"])
                                    }

                                    __scrollHook()
                                </script>
                            {/if}
                        {else}
                            {include "../components/content_error.xml", description => tr("no_results_by_this_query")}
                        {/if}
                    </div>
                    <div class='page_wrap_content_options verticalGrayTabsWrapper'>
                        <div class="page_wrap_content_options_list verticalGrayTabs with_padding">
                            <a n:attr="id => $section === 'users' ? 'used'"    href="/search?section=users&q={urlencode($query)}">    {_s_people}</a>
                            <a n:attr="id => $section === 'groups' ? 'used'"   href="/search?section=groups&q={urlencode($query)}">   {_s_groups}</a>
                            <a n:attr="id => $section === 'posts' ? 'used'"    href="/search?section=posts&q={urlencode($query)}">    {_s_posts}</a>
                            <a n:attr="id => $section === 'videos' ? 'used'"   href="/search?section=videos&q={urlencode($query)}">   {_s_videos}</a>
                            <a n:attr="id => $section === 'apps' ? 'used'"     href="/search?section=apps&q={urlencode($query)}">     {_s_apps}</a>
                            <a n:attr="id => $section === 'audios' ? 'used'"   href="/search?section=audios&q={urlencode($query)}">   {_s_audios}</a>
                            <a n:attr="id => $section === 'audios_playlists' ? 'used'" href="/search?section=audios_playlists&q={urlencode($query)}">{_s_audios_playlists}</a>
                            <a n:attr="id => $section === 'docs' ? 'used'" href="/search?section=docs&q={urlencode($query)}">{_s_documents}</a>
                        </div>

                        <div class='page_search_options'>
                            {include searchOptions}
                        </div>
                    </div>
                </div>

                <div n:if='$paginatorConf->pageCount > 1' class='page_content_paginator_bottom'>
                    {include "../components/paginator.xml", conf => $extendedPaginatorConf}
                </div>
            </div>
        </div>
    </div>
{/block}

{block searchOptions}
    <div class="search_option">
        <div class="search_option_name">
            <div class='search_option_name_ico'></div>
            {_s_order_by}
        </div>
        <div class="search_option_content">
            <select name="order" form="search_form" data-default='id'>
                {if $section == "users"}
                    <option value="id" n:attr="selected => $order == 'id'">{_s_order_by_reg_date}</option>
                    
                    {if OPENVK_ROOT_CONF["openvk"]["preferences"]["commerce"]}
                        <option value="rating" n:attr="selected => $order == 'rating'">{_s_order_by_rating}</option>
                    {/if}
                {elseif $section == "posts"}
                    <option value="id" n:attr="selected => $order == 'id'">{_s_order_by_publishing_date}</option>
                {elseif $section == "audios"}
                    <option value="id" n:attr="selected => $order == 'id'">{_s_order_by_upload_date}</option>
                {else}
                    <option value="id" n:attr="selected => $order == 'id'">{_s_order_by_creation_date}</option>
                {/if}

                {if $section == "audios" || $section == "audios_playlists"}
                    <option value="length" n:attr="selected  => $order == 'length'">{_s_order_by_length}</option>
                    <option value="listens" n:attr="selected => $order == 'listens'">{_s_order_by_listens}</option>
                {/if}
            </select>

            <label n:if="$order != 'rating'">
                <input type="checkbox" name="invert" value="1" form="search_form" n:attr="checked => $_REQUEST['invert'] == '1'">
                {_s_order_invert}
            </label>
        </div>
    </div>

    <div n:if="$section == 'users'" class="search_option">
        <div class="search_option_name">
            <div class='search_option_name_ico'></div>
            {_main}
        </div>
        <div class="search_option_content">
            <input type="text" n:attr="value => $_REQUEST['city']" form="search_form" placeholder="{_city}" name="city">
            <input type="text" n:attr="value => $_REQUEST['hometown']" form="search_form" placeholder="{_hometown}" name="hometown">
            
            <label>
                <input name="is_online" type="checkbox" n:attr="checked => $_REQUEST['is_online'] == '1'" form="search_form" value="1">
                {_s_now_on_site}
            </label>
        </div>
    </div>

    <div n:if="$section == 'users'" class="search_option">
        <div class="search_option_name">
            <div class='search_option_name_ico'></div>
            {_pronouns}
        </div>
        <div class="search_option_content">
            <label><input type="radio" form="search_form" n:attr="checked => $_REQUEST['gender'] == 0" name="gender" value="0">{_male}</label>
            <label><input type="radio" form="search_form" n:attr="checked => $_REQUEST['gender'] == 1" name="gender" value="1">{_female}</label>
            <label><input type="radio" form="search_form" n:attr="checked => $_REQUEST['gender'] == 2" name="gender" value="2">{_neutral}</label>
            <label><input type="radio" form="search_form" n:attr="checked => is_null($_REQUEST['gender']) || $_REQUEST['gender'] == 3" name="gender" data-default='1' value="3">{_s_any}</label>
        </div>
    </div>
    <div n:if="$section == 'users'" n:class="search_option, !isset($_REQUEST['polit_views']) && !isset($_REQUEST['marital_status']) ? search_option_hidden">
        <div class="search_option_name">
            <div class='search_option_name_ico'></div>
            {_s_additional}
        </div>
        <div class="search_option_content">
            <label>
                {_politViews}
                <select name="polit_views" form="search_form" data-default='0'>
                    <option n:foreach="range(0, 9) as $i" value="{$i}" n:attr="selected => $_REQUEST['polit_views'] == $i">
                        {tr("politViews_".$i)}
                    </option>
                </select>
            </label>

            <label>
                {_relationship}
                <select name="marital_status" form="search_form" data-default='0'>
                    <option n:foreach="range(0, 8) as $i" value="{$i}" n:attr="selected => $_REQUEST['marital_status'] == $i">
                        {tr("relationship_".$i)}
                    </option>
                </select>
            </label>
        </div>
    </div>
    <div n:if="$section == 'videos'" class="search_option">
        <div class="search_option_name">
            <div class='search_option_name_ico'></div>
            {_s_main}
        </div>
        <div class="search_option_content">
            <label>
                <input type="checkbox" value='1' name="only_youtube" n:attr="checked => !empty($_REQUEST['only_youtube'])" form="search_form">{_s_only_youtube}
            </label>
        </div>
    </div>
    <div n:if="$section == 'docs'" class="search_option">
        <div class="search_option_name">
            <div class='search_option_name_ico'></div>
            {_s_type}
        </div>
        <div class="search_option_content">
            <select name="type" form="search_form" data-default='0'>
                <option n:foreach="range(0, 8) as $i" value="{$i}" n:attr="selected => $_REQUEST['type'] == $i">
                    {tr("document_type_".$i)}
                </option>
            </select>
        </div>
    </div>
    <div n:if="$section == 'audios'" class="search_option">
        <div class="search_option_name">
            <div class='search_option_name_ico'></div>
            {_s_main}
        </div>
        <div class="search_option_content">
            <label>
                <input type="checkbox" name="only_performers" n:attr="checked => !empty($_REQUEST['only_performers'])" form="search_form">{_s_only_performers}
            </label>
            <label>
                <input type="checkbox" name="with_lyrics" n:attr="checked => !empty($_REQUEST['with_lyrics'])" form="search_form">{_s_with_lyrics}
            </label>
            <label>
                {_genre}
                <select name='genre' form="search_form" data-default='any'>
                    <option n:attr="selected: empty($_REQUEST['genre'])" value="any">{_s_any_single}</option>
                    <option n:foreach='\openvk\Web\Models\Entities\Audio::genres as $genre' n:attr="selected: $_REQUEST['genre'] == $genre" value="{$genre}">
                        {$genre}
                    </option>
                </select>
            </label>
        </div>
    </div>

    <input class="button" id="search_reset" type="button" value="{_reset}">
{/block}
