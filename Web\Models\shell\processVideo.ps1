$ovkRoot = $args[0]
$file    = $args[1]
$dir     = $args[2]
$hash    = $args[3]
$hashT   = $hash.substring(0, 2)
# Create temp files with proper extensions for ffmpeg
$temp    = [System.IO.Path]::GetTempFileName()
$temp2   = [System.IO.Path]::ChangeExtension([System.IO.Path]::GetTempFileName(), ".mp4")

$logFile = "$ovkRoot\ffmpeg_log.txt"

"==== Script Started ====" | Out-File $logFile -Append
"Args: ovkRoot=$ovkRoot, file=$file, dir=$dir, hash=$hash, hashT=$hashT" | Out-File $logFile -Append
"Temp1: $temp" | Out-File $logFile -Append
"Temp2: $temp2" | Out-File $logFile -Append

$shell = Get-WmiObject Win32_process -filter "ProcessId = $PID"
$shell.SetPriority(16384)

# Check source file before move
if (Test-Path $file) {
    "Source file exists before move" | Out-File $logFile -Append
    $sourceSize = (Get-Item $file).Length
    "Source file size: $sourceSize" | Out-File $logFile -Append

    # Check if source file is empty before attempting to move
    if ($sourceSize -eq 0) {
        "ABORT: Source file is empty, cannot process" | Out-File $logFile -Append
        exit 1
    }
} else {
    "Source file does not exist before move" | Out-File $logFile -Append
    "ABORT: Source file missing, cannot process" | Out-File $logFile -Append
    exit 1
}

# Use Copy-Item instead of Move-Item to avoid file locking issues
try {
    Copy-Item $file $temp -Force
    "File copied successfully" | Out-File $logFile -Append

    # Verify the copy was successful
    if (Test-Path $temp) {
        $tempSize = (Get-Item $temp).Length
        "Temp file exists after copy" | Out-File $logFile -Append
        "Temp file size: $tempSize" | Out-File $logFile -Append

        # If temp file is empty, abort
        if ($tempSize -eq 0) {
            "ABORT: Temp file is empty after copy, not running ffmpeg" | Out-File $logFile -Append
            exit 1
        }

        # Only remove original file after successful copy verification
        try {
            Remove-Item $file -Force
            "Original file removed successfully" | Out-File $logFile -Append
        } catch {
            "Warning: Could not remove original file: $($_.Exception.Message)" | Out-File $logFile -Append
            # Continue processing even if we can't remove the original
        }
    } else {
        "ABORT: Temp file does not exist after copy" | Out-File $logFile -Append
        exit 1
    }
} catch {
    "ABORT: Failed to copy file: $($_.Exception.Message)" | Out-File $logFile -Append
    exit 1
}

ffmpeg -i $temp -ss 00:00:01.000 -vframes 1 "$dir$hashT/$hash.gif" 2>&1 | Out-File $logFile -Append
"ffmpeg thumbnail exit code: $LASTEXITCODE" | Out-File $logFile -Append

ffmpeg -i $temp -c:v libx264 -q:v 7 -c:a libmp3lame -q:a 4 -tune zerolatency -vf "scale='if(gt(iw,ih),min(640,iw),-2):if(gt(iw,ih),-2,min(640,ih))'" -y $temp2 2>&1 | Out-File $logFile -Append
"ffmpeg transcode exit code: $LASTEXITCODE" | Out-File $logFile -Append

Move-Item $temp2 "$dir$hashT/$hash.mp4"
Remove-Item $temp
# $temp2 was moved, so no need to remove it

"==== Script Finished ====" | Out-File $logFile -Append
