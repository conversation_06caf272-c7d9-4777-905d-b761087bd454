$ovkRoot = $args[0]
$file    = $args[1]
$dir     = $args[2]
$hash    = $args[3]
$hashT   = $hash.substring(0, 2)
# Create temp files with proper extensions for ffmpeg
$temp    = [System.IO.Path]::GetTempFileName()
$temp2   = [System.IO.Path]::ChangeExtension([System.IO.Path]::GetTempFileName(), ".mp4")

$logFile = "$ovkRoot\ffmpeg_log.txt"

"==== Script Started ====" | Out-File $logFile -Append
"Args: ovkRoot=$ovkRoot, file=$file, dir=$dir, hash=$hash, hashT=$hashT" | Out-File $logFile -Append
"Temp1: $temp" | Out-File $logFile -Append
"Temp2: $temp2" | Out-File $logFile -Append

$shell = Get-WmiObject Win32_process -filter "ProcessId = $PID"
$shell.SetPriority(16384)

# Check source file before move
if (Test-Path $file) {
    "Source file exists before move" | Out-File $logFile -Append
    $sourceSize = (Get-Item $file).Length
    "Source file size: $sourceSize" | Out-File $logFile -Append

    # Check if source file is empty before attempting to move
    if ($sourceSize -eq 0) {
        "ABORT: Source file is empty, cannot process" | Out-File $logFile -Append
        exit 1
    }
} else {
    "Source file does not exist before move" | Out-File $logFile -Append
    "ABORT: Source file missing, cannot process" | Out-File $logFile -Append
    exit 1
}

# Use Copy-Item instead of Move-Item to avoid file locking issues
try {
    Copy-Item $file $temp -Force
    "File copied successfully" | Out-File $logFile -Append

    # Verify the copy was successful
    if (Test-Path $temp) {
        $tempSize = (Get-Item $temp).Length
        "Temp file exists after copy" | Out-File $logFile -Append
        "Temp file size: $tempSize" | Out-File $logFile -Append

        # If temp file is empty, abort
        if ($tempSize -eq 0) {
            "ABORT: Temp file is empty after copy, not running ffmpeg" | Out-File $logFile -Append
            exit 1
        }

        # Only remove original file after successful copy verification
        try {
            Remove-Item $file -Force
            "Original file removed successfully" | Out-File $logFile -Append
        } catch {
            "Warning: Could not remove original file: $($_.Exception.Message)" | Out-File $logFile -Append
            # Continue processing even if we can't remove the original
        }
    } else {
        "ABORT: Temp file does not exist after copy" | Out-File $logFile -Append
        exit 1
    }
} catch {
    "ABORT: Failed to copy file: $($_.Exception.Message)" | Out-File $logFile -Append
    exit 1
}

# Generate thumbnail with non-interactive flags
ffmpeg -y -i $temp -ss 00:00:01.000 -vframes 1 -loglevel error "$dir$hashT/$hash.gif" 2>&1 | Out-File $logFile -Append
"ffmpeg thumbnail exit code: $LASTEXITCODE" | Out-File $logFile -Append

# Transcode video with simplified scaling filter
"Starting video transcode..." | Out-File $logFile -Append
ffmpeg -y -i $temp -c:v libx264 -q:v 7 -c:a libmp3lame -q:a 4 -tune zerolatency -vf "scale=640:480:force_original_aspect_ratio=decrease,pad=640:480:(ow-iw)/2:(oh-ih)/2" -loglevel info $temp2 2>&1 | Out-File $logFile -Append
"ffmpeg transcode exit code: $LASTEXITCODE" | Out-File $logFile -Append

# Check temp2 file after transcoding
if (Test-Path $temp2) {
    $temp2Size = (Get-Item $temp2).Length
    "Temp2 file exists after transcode" | Out-File $logFile -Append
    "Temp2 file size: $temp2Size" | Out-File $logFile -Append

    if ($temp2Size -eq 0) {
        "ERROR: Temp2 file is empty after transcode!" | Out-File $logFile -Append
    }
} else {
    "ERROR: Temp2 file does not exist after transcode!" | Out-File $logFile -Append
}

# Move the processed video to final location
"Moving temp2 to final location: $dir$hashT/$hash.mp4" | Out-File $logFile -Append
Move-Item $temp2 "$dir$hashT/$hash.mp4"
"Move operation completed" | Out-File $logFile -Append

# Check final file
$finalPath = "$dir$hashT/$hash.mp4"
if (Test-Path $finalPath) {
    $finalSize = (Get-Item $finalPath).Length
    "Final video file exists" | Out-File $logFile -Append
    "Final video file size: $finalSize" | Out-File $logFile -Append

    if ($finalSize -eq 0) {
        "ERROR: Final video file is empty!" | Out-File $logFile -Append
    }
} else {
    "ERROR: Final video file does not exist!" | Out-File $logFile -Append
}

Remove-Item $temp
# $temp2 was moved, so no need to remove it

"==== Script Finished ====" | Out-File $logFile -Append