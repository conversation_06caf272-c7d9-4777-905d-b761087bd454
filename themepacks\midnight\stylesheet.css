* {
    scrollbar-color: #3f365b #1d192b;
}

html {
    color-scheme: dark !important;
}

body,
#backdropDripper,
#standaloneCommentBox,
.ovk-photo-view,
.articleView {
    background-color: #0e0b1a;
    color: #c6d2e8;
}

#backdropDripper {
    box-shadow: -30px 0px 20px 20px #0e0b1a, -50px 0px 20px 20px hsl(252 41% 7% / 59%), -70px 0px 20px 20px hsl(252 41% 7% / 43%), -90px 0px 20px 20px hsl(252 41% 7% / 35%), -110px 0px 20px 20px hsl(252 41% 7% / 28%), -130px 0px 20px 20px hsl(252 41% 7% / 16%), 30px 0px 20px 20px #0e0b1a, 50px 0px 20px 20px hsl(252 41% 7% / 59%), 70px 0px 20px 20px hsl(252 41% 7% / 43%), 90px 0px 20px 20px hsl(252 41% 7% / 35%), 110px 0px 20px 20px hsl(252 41% 7% / 28%), 130px 0px 20px 20px hsl(252 41% 7% / 16%);
}

span,
.post-author .date,
.crp-entry--message---text,
.messenger-app--messages---message .time,
.navigation-lang .link_new,
.tippy-box text,
.articleView_author > div > span > a {
    color: #8b9ab5 !important;
}

.nobold,
nobold {
    color: #6f82a8;
}

.page_status {
    color: #c6d2e8;
}

.profileName h2 {
    color: #8eb2d0;
}

.wrap1,
.wrap2,
.page-wrap,
#wrapH,
#wrapHI {
    border-color: #1c202f;
}

.accountInfo,
.left_small_block,
#profile_link,
.profile_link,
.navigation .link,
.navigation .link:hover,
.navigation_footer .link,
.navigation_footer .link:hover,
.completeness-gauge,
input[type="text"],
input[type="password"],
input[type~="text"],
input[type~="password"],
input[type="email"],
input[type="phone"],
input[type~="email"],
input[type~="phone"],
input[type="search"],
input[type~="search"],
input[type~="date"],
select,
.content_title_expanded,
.content_title_unexpanded,
.content_subtitle,
textarea,
.post-content,
.post-author,
hr,
h4,
.postFeedWrapper,
.tabs,
#wallAttachmentMenu,
.ovk-diag,
.ovk-diag-head,
#ovkDraw,
#ovkDraw .literally .lc-picker,
.literally .lc-options.horz-toolbar,
.page_wrap,
.container_gray .content,
.summaryBar,
.groups_options,
form[action="/search"]>input,
.header_search_input,
.header_search_inputbt,
.accent-box,
.page_status_popup,
.messenger-app--input,
.messenger-app,
.crp-entry:first-of-type,
.crp-list,
.crp-entry,
.note_footer,
.page_content>div,
#editor,
.note_header,
center[style="background: white;border: #DEDEDE solid 1px;"],
.album-photo img,
.mb_tabs,
.mb_tab#active div,
.navigation-lang .link_new,
#faqhead,
#faqcontent,
.post-divider,
.comment,
.commentsTextFieldWrap,
tr,
td,
th,
#votesBalance, 
#news,
.paginator a.active,
.paginator a:hover,
.topic-list-item,
#userContent blockquote,
.tippy-box[data-theme~="vk"],
.poll,
#standaloneCommentBox,
#search_box_button,
.verticalGrayTabs #used,
.search_option_name,
.borderup,
#tour,
#auth,
.ovk-photo-view,
.page_wrap_content_main .def_row_content,
.topGrayBlock,
.bigPlayer,
input[type="number"],
.like_tooltip_wrapper .like_tooltip_body,
.like_tooltip_wrapper .like_tooltip_head {
    border-color: #2c2640 !important;
}

.like_tooltip_wrapper .like_tooltip_head {
    background: linear-gradient(180deg, #383052, #231e33) !important;
    box-shadow: unset !important;
}

.post-upload,
.post-has-poll,
.post-has-note {
    color: #777;
}

.tippy-box[data-theme~="vk"][data-placement^='top']>.tippy-arrow::before,
.tippy-box[data-theme~="vk"][data-placement^='bottom']>.tippy-arrow::before {
    border-top-color: #1e1a2b;
    border-bottom-color: #1e1a2b;
}

hr {
    background-color: #2c2640 !important;
}

.cookies-popup {
    background: linear-gradient(#1e1a2b, #231e33);
    box-shadow: unset;
}

.button,
#activetabs,
.messagebox-content-header,
.accent-box,
.button_search,
.header_navigation #search_box .search_box_button {
    background-color: #383052;
}

.header_navigation #search_box .search_box_button {
    box-shadow: 0px 2px 0px 0px rgb(70, 63, 96) inset;
}

.search_option_name {
    background-color: #383052 !important;
    color: lightgrey !important;
}

.tab:hover,
.ntSelect:hover {
    background-color: #40375e;
}

.menu_divider,
.ovk-diag-action,
.minilink .counter,
.topGrayBlock,
#show_more,
.information {
    background-color: #2c2640 !important;
}

.bsdn_contextMenu {
    background-color: #1f1a2c;
    border-color: #2c2640;
}

.bsdn_contextMenuElement:hover,
.verticalGrayTabs li:hover {
    background-color: #29223a;
}

#ovkDraw .literally .lc-picker,
.literally .lc-options.horz-toolbar,
.mb_tab#active {
    background-color: #453e5e !important;
}

.ovk-diag-cont {
    background-color: #272e4894;
}

a,
.page_footer .link,
#profile_link,
.profile_link {
    color: #8fb9d8;
}

.page_footer .link:hover,
.navigation .link:hover,
.navigation .edit-button:hover,
#profile_link:hover,
.profile_link:hover,
#wallAttachmentMenu>a:hover,
.crp-entry:hover,
.navigation-lang .link_new:hover,
.paginator a:hover,
.post-share-button:hover,
.post-like-button:hover,
#search_box_button:active,
.mb_tab:hover {
    background-color: #272138 !important;
}

.header_navigation .link a,
.searchOptionName {
    color: #bcc3d0;
}

.header_navigation .link a:hover,
.home_button_custom {
    color: #c7cdd9;
}

.navigation .link {
    color: #d9e0ee;
}

.navigation .edit-button {
    background-color: #0e0b1a !important;
    color: #7b94c4 !important;
}

#test-label,
.msg.msg_err {
    background-color: #30161d;
}

.msg.msg_succ {
    background-color: #163f13;
}

h4,
.content_title_expanded,
.summaryBar .summary,
.content_title_unexpanded {
    color: #7c94c5;
}

.notes_titles small,
.post-upload,
.post-has-poll {
    color: #6f82a8;
}

.content_title_expanded,
.content_title_unexpanded,
.ovk-diag,
.settings_delete,
center[style="background: white;border: #DEDEDE solid 1px;"],
.album-photo img,
#faqhead,
td.e,
tr.e,
.playlistListView:hover, 
.playlistListView .playlistCover,
.photosInsert > div,
.attachment_selector #attachment_insert #attachment_insert_count {
    background-color: #231e33 !important;
}

.content_subtitle,
.postFeedWrapper,
.ovk-diag-head,
.container_gray,
.page_status_popup,
.messenger-app--input,
.note_header,
#faqcontent,
.commentsTextFieldWrap,
td.v,
tr.v,
#votesBalance,
#news,
.expand_button,
#userContent blockquote,
.tippy-box[data-theme~="vk"],
.searchOptions,
.like_tooltip_wrapper .like_tooltip_body {
    background-color: #1e1a2b !important;
}

.post-author {
    background-color: #1e1a2b;
    /* this is fix to correct the unexpected behavior of the microblog style lol */
}

.content_title_expanded {
    background-image: url("/themepack/midnight/*******/resource/flex_arrow_open.png") !important;
}

.content_title_unexpanded {
    background-image: url("/themepack/midnight/*******/resource/flex_arrow_shut.gif") !important;
}

.ovk-video>.preview,
.video-preview {
    box-shadow: inset 0 0 0 1px #231e33, inset 0 0 0 2px #1e1a2b;
}

#wallAttachmentMenu,
.container_gray .content,
.mb_tabs {
    background-color: #120e1f;
}

#wallAttachmentMenu>.header,
.messenger-app--messages---message.unread,
tr.h {
    background-color: #1d192b;
}

.toTop {
    background-color: #272138;
    color: #c6d2e8;
}

.page_yellowheader {
    color: #c6d2e8;
    background-image: url("/themepack/midnight/*******/resource/header_purple.png") !important;
    background-color: #231f34;
    border-color: #231f34;
}

.page_header {
    background-image: url("/themepack/midnight/*******/resource/header.png") !important;
}

.page_custom_header {
    background-image: url("/themepack/midnight/*******/resource/header_custom.png") !important;
}

.page_yellowheader span,
.page_yellowheader a {
    color: #a48aff !important;
}

.completeness-gauge,
.poll-result-bar {
    background-color: #231e33;
}

.completeness-gauge>div,
.poll-result-bar-sub {
    background-color: #2c2640;
}

form[action="/search"]>input,
.header_search_input,
textarea,
input[type="text"],
input[type="password"],
input[type~="text"],
input[type~="password"],
input[type="email"],
input[type="phone"],
input[type~="email"],
input[type~="phone"],
input[type="search"],
input[type~="search"],
input[type~="date"],
input[type="number"],
select,
.crp-entry--message.unread {
    background-color: #181826 !important;
}

input[type="checkbox"] {
    background-image: url("/themepack/midnight/*******/resource/checkbox.png") !important;
}

input[type="radio"] {
    background-image: url("/themepack/midnight/*******/resource/radio.png") !important;
}

.header_navigation .link, .header_navigation .header_divider_stick {
    background: unset;
}

.heart {
    background-image: url("/themepack/midnight/*******/resource/like.gif") !important;
}

.pinned-mark,
.post-author .pin {
    background-image: url("/themepack/midnight/*******/resource/pin.png") !important;
}

.repost-icon {
    background-image: url("/themepack/midnight/*******/resource/published.gif") !important;
}

.post-author .delete {
    background-image: url("/themepack/midnight/*******/resource/input_clear.gif") !important;
}

.user-alert {
    background-color: #41311a;
    color: #d5b88c;
    border-color: #514534;
}

#search_box_button {
    box-shadow: 0px 2px 0px 0px rgba(111, 111, 111, 0.18) inset;
}

#search_box_button:active {
    box-shadow: 0px -2px 0px 0px rgba(255, 255, 255, 0.18) inset;
}

.verticalGrayTabsWrapper {
    background: #1e1a2b;
    border-top: 1px solid #2c2640;
    border-left: 1px solid #2a2841;
}

.page_content_paginator_bottom {
    background: #1e1a2b;
    border-top: 1px solid #2c2640;
}

.verticalGrayTabs #used {
    background: #463f60 !important;
}

#backdropEditor {
    background-image: url("/themepack/midnight/*******/resource/backdrop-editor.gif") !important;
    border-color: #473e66 !important;
}

.sugglist {
    background-color: #231e33;
    border-color: #2c2640 !important;
}

.sugglist a {
    color: #7c94c5;
}

.button.loaded {
    background: #383052 url("/assets/packages/static/openvk/img/loading_mini.gif") no-repeat 50% 50% !important;
}

.bigPlayer {
    background-color: rgb(30, 26, 43) !important;
}

.bigPlayer .selectableTrack,
.audioEmbed .track>.selectableTrack {
    border-top: #b9b9b9 1px solid !important;
}

.bigPlayer .slider,
.audioEmbed .track .slider {
    background: #b9b9b9 !important;
}

.audioEntry.nowPlaying {
    background: #463f60 !important;
    outline: 1px solid #645a86 !important;
}

.preformer, .trackPerformers a {
    color: #b7b7b7 !important;
}

.tip_result {
    background: #b9b9b9 !important;
    color: black !important;
}

.audioEntry.nowPlaying:hover {
    background: #50486f !important;
}

.audioEntry:hover {
    background: #19142D !important;
}

.audioEntry .performer a,
.bigPlayer .trackInfo a {
    color: #a2a1a1 !important;
}

.musicIcon.lagged {
    opacity: 49%;
}

.verticalGrayTabs a {
    color: #bbb !important;
}

.verticalGrayTabs a:hover {
    color: #eeeeee !important;
    background: #332d46 !important;
}

.friendsAudiosList .elem:hover {
    background: #332d46 !important;
}

.audioEntry .playerButton .playIcon {
    filter: invert(81%);
}

.load_bar {
    background: #2c2839 !important;
    border-bottom-color: #151418 !important;
}

#ajax_audio_player, #ajloader {
    box-shadow: rgb(58 53 73) 0px 0px 2px 3px;
}

img[src$='/assets/packages/static/openvk/img/camera_200.png'],
img[src$='/assets/packages/static/openvk/img/song.jpg'] {
    filter: invert(100%);
}

.audioStatus {
    color: #8E8E8E !important;
}

.audioEntry .withLyrics {
    color: #9481d9 !important;
}

#listensCount {
    color: unset !important;
}

#upload_container,
.whiteBox {
    background: #1d1928 !important;
    border: 1px solid #383052 !important;
}

ul {
    color: #8b9ab5 !important;
}

#audio_upload {
    border: 2px solid #383052 !important;
    background-color: #262133 !important;
}

/* вот бы css в овк был бы написан на var()'ах( */
#upload_container.uploading {
    background-color: #312b3f !important;
    background-image: url('/assets/packages/static/openvk/img/progressbar.gif') !important;
}

.musicIcon.pressed {
    opacity: 41% !important;
}

.ovk-diag-body .searchBox {
    background: #1e1a2b !important;
}

.audioEntry.nowPlaying .title {
    color: #fff !important;
}

.attachAudio:hover {
    background: #19142D !important;
    cursor: pointer;
}

/* Tour */
.rightNav h1 {
    background: #000;
}

.tabcontent {
    background: #2c2640 !important;
}

.add_image_text {
    z-index: 999;
}

.content_page_error {
    background: #28223a;
    border: #2c2640 solid 1px;
}

#backdropFilePicker {
    margin: 15px !important;
}

#upload_container .upload_container_element {
    border: 1px solid #383052;
    background: #1e1b2a;
}

#docs_page_wrapper .docs_page_search {
    background: #1d1a27;
    border-bottom: #2a2841 solid 1px;
}

.docListViewItem {
    border-bottom-color: #2a2841;
}

.docListViewItem:hover, .attachButton:hover {
    background: #271c48;
}

.docListViewItem .doc_icon {
    background: #33255e;
}

.docListViewItem .doc_content b {
    color: #7c94c5;
}

.doc_icon.no_image span::before {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAkAAAAKCAYAAABmBXS+AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsAAAA7AAWrWiQkAAABXSURBVChTY2RAAoFe9ZuEBOR93314uHn9tkY/qDADE5QGA5ACZBoGUBThAkQpYkyOmvcfysYJiDMJROAzbe6yJEZ4EGBTCFIAolHCCVkhTAFWgGkiAwMAzxkZ3qVQ7YEAAAAASUVORK5CYII=");
}

.bigPlayer .bigPlayerWrapper .absoluteButtons > div {
    background: #1e1a2b;
    border: 1px solid #2c2640;
}

.insertedPhoto {
    background: #1e1a2b;
    border: 1px solid #403a56;
}

.ovk-modal-player-window #ovk-player-info {
    background: #0e0b1a;
}

.header_navigation #search_box #searchBoxFastTips {
    background: #181826;
    border-color: #2c2640;
}

.header_navigation #search_box #searchBoxFastTips a:hover, .header_navigation #search_box #searchBoxFastTips a:focus {
    background: #111322;
}
