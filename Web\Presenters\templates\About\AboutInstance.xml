{extends "../@layout.xml"}
{block title}{_about_this_instance}{/block}

{block header}
    {_about_this_instance}
{/block}

{block content}
    <table width="100%" cellspacing="0" cellpadding="0">
        <tbody>
            <tr valign="top">
                <td width="250"{if sizeof($admins) > 0} style="padding-right: 10px;"{/if}>
                    <h4>{_statistics}</h4>
                    <div style="margin-top: 5px;">
                        {_on_this_instance_are}
                        <ul>
                            <li><span>{tr("about_users", $usersStats->all)|noescape}</span></li>
                            <li><span>{tr("about_online_users", $usersStats->online)|noescape}</span></li>
                            <li><span>{tr("about_active_users", $usersStats->active)|noescape}</span></li>
                            <li><span>{tr("about_groups", $clubsCount)|noescape}</span></li>
                            <li><span>{tr("about_wall_posts", $postsCount)|noescape}</span></li>
                        </ul>
                    </div>
                    {if OPENVK_ROOT_CONF['openvk']['preferences']['about']['links']}
                        <h4>{_about_links}</h4>
                        <div style="margin-top: 5px;">
                            {_instance_links}
                            <ul>
                                <li n:foreach="OPENVK_ROOT_CONF['openvk']['preferences']['about']['links'] as $aboutLink"><a href="{$aboutLink['url']}" target="_blank" class="link">{$aboutLink["name"]}</a></li>
                            </ul>
                        </div>
                    {/if}
                </td>
                <td n:if="sizeof($admins) > 0">
                    <h4>{_administrators}</h4>
                    <div style="margin-left: 5px; margin-top: 5px;">
                        <div n:foreach="$admins as $admin" class="avatar-list-item">
                            <div class="avatar">
                                <a href="{$admin->getURL()}">
                                    <img class="ava" src="{$admin->getAvatarUrl()}" />
                                </a>
                            </div>
                            {* Это наверное костыль, ну да ладно *}
                            <div n:class="info, mb_strlen($admin->getCanonicalName()) < 22 ? info-centered">
                                <a href="{$admin->getURL()}" class="title">{$admin->getCanonicalName()}</a>
                            </div>
                        </div>
                    </div>
                </td>
            </tr>
        </tbody>
    </table>

    {if sizeof($popularClubs) !== 0}
        <h4>{_most_popular_groups}</h4>
        {var $entries = array_chunk($popularClubs, 10, true)}
        <table width="100%" cellspacing="0" cellpadding="0">
            <tbody>
                <tr valign="top">
                    <td n:foreach="$entries as $chunk">
                        <ol>
                            <li value="{$num+1}" style="margin-top: 5px;" n:foreach="$chunk as $num => $club">
                                <a href="{$club->club->getURL()}">{$club->club->getName()}</a>
                                <div>
                                    {tr("participants", $club->subscriptions)}
                                </div>
                            </li>
                        </ol>
                    </td>
                </tr>
            </tbody>
        </table>
    {/if}

    <h4>{_rules}</h4>
    <div style="margin-top: 16px;">
        {tr("about_watch_rules", "/terms")|noescape}
    </div>
{/block}
