{extends "@layout.xml"}

{block title}
    {_edit}
{/block}

{block heading}
    {_edit} #{$form->id ?? "undefined"}
{/block}

{block content}
<div style="margin: 8px -8px;" class="aui-tabs horizontal-tabs">
    <ul class="tabs-menu">
        <li class="menu-item active-tab">
            <a href="#info">{_admin_banned_link}</a>
        </li>
    </ul>
    <div class="tabs-pane active-pane" id="info">
        <form class="aui" method="POST">
            <div class="field-group">
                <label for="id">ID</label>
                <input class="text long-field" type="number" id="id" name="id" disabled value="{$form->id}" />
            </div>
            <div class="field-group">
                <label for="token">{_admin_banned_domain}</label>
                <input class="text long-field" type="text" id="link" name="link" value="{$form->link}" />
                <div class="description">{_admin_banned_link_description}</div>
            </div>
            <div class="field-group">
                <label for="token">{_admin_banned_link_regexp}</label>
                <input class="text long-field" type="text" id="regexp" name="regexp" value="{$form->regexp}" />
                <div class="description">{_admin_banned_link_regexp_description}</div>
            </div>
            <div class="field-group">
                <label for="coins">{_admin_banned_link_reason}</label>
                <input class="text long-field" type="text" id="reason" name="reason" value="{$form->reason}" />
            </div>
            <div class="buttons-container">
                <div class="buttons">
                    <input type="hidden" name="hash" value="{$csrfToken}" />
                    <input class="aui-button aui-button-primary submit" type="submit" value="{_save}">
                    <a class="aui-button aui-button-secondary" href="/admin/bannedLink/id{$form->id}/unban">{_delete}</a>
                </div>
            </div>
        </form>
    </div>
</div>
{/block}
