{extends "../@layout.xml"}

{block title}
    {if $mode == 'list'}
        {if $ownerId > 0}
            {_audios} {$owner->getMorphedName("genitive", false)}
        {else}
            {_audios_group}
        {/if}
    {elseif $mode == 'new'}
        {_audio_new}
    {elseif $mode == 'uploaded'}
        {_my_audios_small_uploaded}
    {elseif $mode == 'popular'}
        {_audio_popular}
    {elseif $mode == 'alone_audio'}
        {$alone_audio->getName()}
    {else}
        {if $ownerId > 0}
            {_playlists} {$owner->getMorphedName("genitive", false)}
        {else}
            {_playlists_group}
        {/if}
    {/if}
{/block}

{block header}
    <div n:if="$mode == 'list'">
        <div n:if="$isMy">{_my_audios_small}</div>
        <div n:if="!$isMy">
            <a href="{$owner->getURL()}">{$owner->getCanonicalName()}</a>
            »
            {_audios}
        </div>
    </div>

    <div n:if="$mode == 'uploaded'">
        {_my_audios_small}
        »
        {_my_audios_small_uploaded}
    </div>

    <div n:if="$mode == 'new'">
        {_audios}
        »
        {_audio_new}
    </div>

    <div n:if="$mode == 'popular'">
        {_audios}
        »
        {_audio_popular}
    </div>

    <div n:if="$mode == 'playlists'">
        {_audios}
        »
        {if $isMy}{_my_playlists}{else}{_playlists}{/if}
    </div>

    <div n:if="$mode == 'alone_audio'">
        {_my_audios_small}
    </div>
{/block}

{block content}
    {* ref: https://archive.li/P32em *}
    
    {include "bigplayer.xml", buttonsShow_summary => $audiosCount > 10}

    <script>
        window.__current_page_audio_context = null
        {if $mode == 'list'}
            window.__current_page_audio_context = {
                name: 'entity_audios',
                entity_id: {$ownerId},
                page: {$page}
            }
        {elseif $mode == 'uploaded'}
            window.__current_page_audio_context = {
                name: 'uploaded',
                entity_id: 0,
                page: {$page}
            }
        {elseif $mode == 'alone_audio'}
            window.__current_page_audio_context = {
                name: 'alone_audio',
                entity_id: {$alone_audio->getId()},
                page: 1
            }
        {/if}
    </script>

    <div n:if="isset($audios)" class='summaryBarHideable summaryBar summaryBarFlex padding' style="margin: 0px -10px;width: 99.5%;display: none;">
        <div class='summary'>
            <b>{tr("is_x_audio", $audiosCount)}</b>
        </div>

        {include "../components/paginator.xml", conf => (object) [
            "page"     => $page,
            "count"    => $audiosCount,
            "amount"   => sizeof($audios),
            "perPage"  => $perPage ?? OPENVK_DEFAULT_PER_PAGE,
            "atTop"    => true,
            "space"    => 6,
            "tidy"     => true,
        ]}
    </div>

    <div class="audiosDiv">
        <div class="audiosContainer audiosSideContainer audiosPaddingContainer" n:if="$mode != 'playlists'">
            <div n:if="$audiosCount <= 0" style='height: 100%;'>
                {include "../components/content_error.xml", description => $ownerId > 0 ? ($ownerId == $thisUser->getId() ? tr("no_audios_thisuser") : tr("no_audios_user")) : tr("no_audios_club")}
            </div>
            <div n:if="$audiosCount > 0" class="scroll_container">
                <div class="scroll_node" n:foreach="$audios as $audio">
                    {include "player.xml", audio => $audio, club => $club}
                </div>
            </div>

            <div n:if="$mode != 'new' && $mode != 'popular'">
                {include "../components/paginator.xml", conf => (object) [
                    "page"     => $page,
                    "count"    => $audiosCount,
                    "amount"   => sizeof($audios),
                    "perPage"  => $perPage ?? OPENVK_DEFAULT_PER_PAGE,
                    "atBottom" => true,
                ]}
            </div>
        </div>
        
        <div class="audiosPaddingContainer audiosSideContainer audiosPaddingContainer" style="width: 72.2%;" n:if="$mode == 'playlists'">
            <div n:if="$playlistsCount <= 0" style='height: 100%;'>
                {include "../components/content_error.xml", description => $ownerId > 0 ? ($ownerId == $thisUser->getId() ? tr("no_playlists_thisuser") : tr("no_playlists_user")) : tr("no_playlists_club")}
            </div>

            <div class="scroll_container playlistContainer" n:if="$playlistsCount > 0">
                <div class='scroll_node' n:foreach='$playlists as $playlist'>
                    {include 'playlistListView.xml', playlist => $playlist}
                </div>
            </div>

            <div>
                {include "../components/paginator.xml", conf => (object) [
                    "page"     => $page,
                    "count"    => $playlistsCount,
                    "amount"   => sizeof($playlists),
                    "perPage"  => $perPage ?? OPENVK_DEFAULT_PER_PAGE,
                    "atBottom" => true,
                ]}
            </div>
        </div>
        {include "tabs.xml"}
    </div>
{/block}
