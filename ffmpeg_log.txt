# Video processing log - debugging empty video issue
==== Script Started ====
Args: ovkRoot=C:\OpenVK\chandler\chandler/../extensions/enabled/openvk, file=C:\Users\<USER>\AppData\Local\Temp\ovk8C79.tmp, dir=C:\OpenVK\chandler\chandler/../extensions/enabled/openvk/storage/, hash=fd573c862bdd3e41e2546d82b38697729f76a43385429012223e1fb83ce72966862c64b3231b442c635dac7f0ebec8c5838c6025a8bb542e487e002061404650, hashT=fd
Temp1: C:\Users\<USER>\AppData\Local\Temp\tmp8E68.tmp
Temp2: C:\Users\<USER>\AppData\Local\Temp\tmp8E69.mp4
Source file exists before move
Source file size: 7817323
File copied successfully
Temp file exists after copy
Temp file size: 7817323
Original file removed successfully
ffmpeg thumbnail exit code: 0
Starting video transcode...
ffmpeg : ffmpeg version 2025-05-15-git-12b853530a-full_build-www.gyan.dev Copyright (c) 2000-2025 the FFmpeg developers
At C:\OpenVK\chandler\extensions\available\openvk\Web\Models\shell\processVideo.ps1:77 char:1
+ ffmpeg -y -i $temp -c:v libx264 -q:v 7 -c:a libmp3lame -q:a 4 -tune z ...
+ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : NotSpecified: (ffmpeg version ...mpeg developers:String) [], RemoteException
    + FullyQualifiedErrorId : NativeCommandError
 
  built with gcc 15.1.0 (Rev4, Built by MSYS2 project)
  configuration: --enable-gpl --enable-version3 --enable-static --disable-w32threads --disable-autodetect 
--enable-fontconfig --enable-iconv --enable-gnutls --enable-lcms2 --enable-libxml2 --enable-gmp --enable-bzlib 
--enable-lzma --enable-libsnappy --enable-zlib --enable-librist --enable-libsrt --enable-libssh --enable-libzmq 
--enable-avisynth --enable-libbluray --enable-libcaca --enable-libdvdnav --enable-libdvdread --enable-sdl2 
--enable-libaribb24 --enable-libaribcaption --enable-libdav1d --enable-libdavs2 --enable-libopenjpeg --enable-libquirc 
--enable-libuavs3d --enable-libxevd --enable-libzvbi --enable-libqrencode --enable-librav1e --enable-libsvtav1 
--enable-libvvenc --enable-libwebp --enable-libx264 --enable-libx265 --enable-libxavs2 --enable-libxeve 
--enable-libxvid --enable-libaom --enable-libjxl --enable-libvpx --enable-mediafoundation --enable-libass 
--enable-frei0r --enable-libfreetype --enable-libfribidi --enable-libharfbuzz --enable-liblensfun --enable-libvidstab 
--enable-libvmaf --enable-libzimg --enable-amf --enable-cuda-llvm --enable-cuvid --enable-dxva2 --enable-d3d11va 
--enable-d3d12va --enable-ffnvcodec --enable-libvpl --enable-nvdec --enable-nvenc --enable-vaapi --enable-libshaderc 
--enable-vulkan --enable-libplacebo --enable-opencl --enable-libcdio --enable-libgme --enable-libmodplug 
--enable-libopenmpt --enable-libopencore-amrwb --enable-libmp3lame --enable-libshine --enable-libtheora 
--enable-libtwolame --enable-libvo-amrwbenc --enable-libcodec2 --enable-libilbc --enable-libgsm --enable-liblc3 
--enable-libopencore-amrnb --enable-libopus --enable-libspeex --enable-libvorbis --enable-ladspa --enable-libbs2b 
--enable-libflite --enable-libmysofa --enable-librubberband --enable-libsoxr --enable-chromaprint
  libavutil      60.  2.100 / 60.  2.100
  libavcodec     62.  3.101 / 62.  3.101
  libavformat    62.  0.102 / 62.  0.102
  libavdevice    62.  0.100 / 62.  0.100
  libavfilter    11.  0.100 / 11.  0.100
  libswscale      9.  0.100 /  9.  0.100
  libswresample   6.  0.100 /  6.  0.100
Input #0, mov,mp4,m4a,3gp,3g2,mj2, from 'C:\Users\<USER>\AppData\Local\Temp\tmp8E68.tmp':
  Metadata:
    major_brand     : iso5
    minor_version   : 512
    compatible_brands: iso5iso6mp41
    encoder         : Lavf61.9.107
    description     : Packed by Bilibili XCoder v2.0.2
  Duration: 00:04:10.29, start: 0.000000, bitrate: 249 kb/s
  Stream #0:0[0x1](und): Video: h264 (Constrained Baseline) (avc1 / 0x31637661), yuv420p(tv, bt709, progressive), 
1280x720 [SAR 1:1 DAR 16:9], 177 kb/s, 30 fps, 30 tbr, 15360 tbn (default)
    Metadata:
      handler_name    : VideoHandler
      vendor_id       : [0][0][0][0]
      encoder         : Lavc61.33.102 libx264
  Stream #0:1[0x2](und): Audio: aac (LC) (mp4a / 0x6134706D), 44100 Hz, mono, fltp, 70 kb/s (default)
    Metadata:
      handler_name    : SoundHandler
      vendor_id       : [0][0][0][0]
Stream mapping:
  Stream #0:0 -> #0:0 (h264 (native) -> h264 (libx264))
  Stream #0:1 -> #0:1 (aac (native) -> mp3 (libmp3lame))
Press [q] to stop, [?] for help
[libx264 @ 0000014c20636e40] -qscale is ignored, -crf is recommended.
[libx264 @ 0000014c20636e40] using SAR=1/1
[libx264 @ 0000014c20636e40] using cpu capabilities: MMX2 SSE2Fast SSSE3 SSE4.2 AVX FMA3 BMI2 AVX2
[libx264 @ 0000014c20636e40] profile High, level 3.0, 4:2:0, 8-bit
[libx264 @ 0000014c20636e40] 264 - core 165 r3215 32c3b80 - H.264/MPEG-4 AVC codec - Copyleft 2003-2025 - 
http://www.videolan.org/x264.html - options: cabac=1 ref=3 deblock=1:0:0 analyse=0x3:0x113 me=hex subme=7 psy=1 
psy_rd=1.00:0.00 mixed_ref=1 me_range=16 chroma_me=1 trellis=1 8x8dct=1 cqm=0 deadzone=21,11 fast_pskip=1 
chroma_qp_offset=-2 threads=7 lookahead_threads=7 sliced_threads=1 slices=7 nr=0 decimate=1 interlaced=0 
bluray_compat=0 constrained_intra=0 bframes=0 weightp=2 keyint=250 keyint_min=25 scenecut=40 intra_refresh=0 rc=crf 
mbtree=0 crf=23.0 qcomp=0.60 qpmin=0 qpmax=69 qpstep=4 ip_ratio=1.40 aq=1:1.00
Output #0, mp4, to 'C:\Users\<USER>\AppData\Local\Temp\tmp8E69.mp4':
  Metadata:
    major_brand     : iso5
    minor_version   : 512
    compatible_brands: iso5iso6mp41
    description     : Packed by Bilibili XCoder v2.0.2
    encoder         : Lavf62.0.102
  Stream #0:0(und): Video: h264 (avc1 / 0x31637661), yuv420p(tv, bt709, progressive), 640x480 [SAR 1:1 DAR 4:3], 
q=2-31, 30 fps, 15360 tbn (default)
    Metadata:
      encoder         : Lavc62.3.101 libx264
      handler_name    : VideoHandler
      vendor_id       : [0][0][0][0]
    Side data:
      cpb: bitrate max/min/avg: 0/0/0 buffer size: 0 vbv_delay: N/A
  Stream #0:1(und): Audio: mp3 (mp4a / 0x6134706D), 44100 Hz, mono, fltp (default)
    Metadata:
      encoder         : Lavc62.3.101 libmp3lame
      handler_name    : SoundHandler
      vendor_id       : [0][0][0][0]
frame=  437 fps=0.0 q=19.0 size=     256KiB time=00:00:14.56 bitrate= 144.0kbits/s speed=28.1x elapsed=0:00:00.51    
frame=  803 fps=780 q=19.0 size=     256KiB time=00:00:26.80 bitrate=  78.3kbits/s speed=  26x elapsed=0:00:01.02    
frame= 1168 fps=757 q=19.0 size=     512KiB time=00:00:38.96 bitrate= 107.6kbits/s speed=25.2x elapsed=0:00:01.54    
frame= 1596 fps=776 q=21.0 size=    1024KiB time=00:00:53.23 bitrate= 157.6kbits/s speed=25.9x elapsed=0:00:02.05    
frame= 1907 fps=741 q=24.0 size=    1024KiB time=00:01:03.60 bitrate= 131.9kbits/s speed=24.7x elapsed=0:00:02.57    
frame= 2267 fps=734 q=19.0 size=    1280KiB time=00:01:15.60 bitrate= 138.7kbits/s speed=24.5x elapsed=0:00:03.08    
frame= 2696 fps=748 q=19.0 size=    1536KiB time=00:01:29.90 bitrate= 140.0kbits/s speed=24.9x elapsed=0:00:03.60    
frame= 3116 fps=756 q=19.0 size=    1792KiB time=00:01:43.90 bitrate= 141.3kbits/s speed=25.2x elapsed=0:00:04.12    
frame= 3578 fps=772 q=19.0 size=    2048KiB time=00:01:59.30 bitrate= 140.6kbits/s speed=25.7x elapsed=0:00:04.63    
frame= 4035 fps=783 q=18.0 size=    2304KiB time=00:02:14.53 bitrate= 140.3kbits/s speed=26.1x elapsed=0:00:05.15    
frame= 4480 fps=789 q=19.0 size=    2560KiB time=00:02:29.36 bitrate= 140.4kbits/s speed=26.3x elapsed=0:00:05.68    
frame= 4930 fps=796 q=19.0 size=    3072KiB time=00:02:44.36 bitrate= 153.1kbits/s speed=26.5x elapsed=0:00:06.19    
frame= 5373 fps=799 q=19.0 size=    3328KiB time=00:02:59.13 bitrate= 152.2kbits/s speed=26.6x elapsed=0:00:06.72    
frame= 5858 fps=809 q=19.0 size=    3584KiB time=00:03:15.30 bitrate= 150.3kbits/s speed=  27x elapsed=0:00:07.23    
frame= 6343 fps=818 q=18.0 size=    3840KiB time=00:03:31.46 bitrate= 148.8kbits/s speed=27.3x elapsed=0:00:07.75    
frame= 6826 fps=826 q=21.0 size=    4096KiB time=00:03:47.56 bitrate= 147.5kbits/s speed=27.5x elapsed=0:00:08.26    
frame= 7308 fps=832 q=19.0 size=    4352KiB time=00:04:03.63 bitrate= 146.3kbits/s speed=27.7x elapsed=0:00:08.78    
[out#0/mp4 @ 0000014c20667800] video:2029KiB audio:2584KiB subtitle:0KiB other streams:0KiB global headers:0KiB muxing 
overhead: 4.760067%
frame= 7507 fps=836 q=19.0 Lsize=    4832KiB time=00:04:10.26 bitrate= 158.2kbits/s speed=27.9x elapsed=0:00:08.98    
[libx264 @ 0000014c20636e40] frame I:51    Avg QP: 3.24  size:  8121
[libx264 @ 0000014c20636e40] frame P:7456  Avg QP: 4.50  size:   223
[libx264 @ 0000014c20636e40] mb I  I16..4: 90.0%  0.6%  9.3%
[libx264 @ 0000014c20636e40] mb P  I16..4:  0.3%  0.0%  0.1%  P16..4:  0.4%  0.0%  0.0%  0.0%  0.0%    skip:99.1%
[libx264 @ 0000014c20636e40] 8x8 transform intra:2.2% inter:24.6%
[libx264 @ 0000014c20636e40] coded y,uvDC,uvAC intra: 8.4% 9.8% 8.9% inter: 0.1% 0.3% 0.1%
[libx264 @ 0000014c20636e40] i16 v,h,dc,p: 74%  2% 23%  0%
[libx264 @ 0000014c20636e40] i8 v,h,dc,ddl,ddr,vr,hd,vl,hu: 28% 12% 58%  1%  0%  0%  0%  0%  0%
[libx264 @ 0000014c20636e40] i4 v,h,dc,ddl,ddr,vr,hd,vl,hu: 25% 22% 28%  4%  4%  3%  4%  4%  5%
[libx264 @ 0000014c20636e40] i8c dc,h,v,p: 95%  2%  3%  0%
[libx264 @ 0000014c20636e40] Weighted P-Frames: Y:0.0% UV:0.0%
[libx264 @ 0000014c20636e40] ref P L0: 93.8%  2.9%  2.6%  0.7%
[libx264 @ 0000014c20636e40] kb/s:66.41
ffmpeg transcode exit code: 0
Temp2 file exists after transcode
Temp2 file size: 4948413
Moving temp2 to final location: C:\OpenVK\chandler\chandler/../extensions/enabled/openvk/storage/fd/fd573c862bdd3e41e2546d82b38697729f76a43385429012223e1fb83ce72966862c64b3231b442c635dac7f0ebec8c5838c6025a8bb542e487e002061404650.mp4
Move operation completed
Final video file exists
Final video file size: 4948413
==== Script Finished ====
==== Script Started ====
Args: ovkRoot=C:\OpenVK\chandler\chandler/../extensions/enabled/openvk, file=C:\Users\<USER>\AppData\Local\Temp\ovk5833.tmp, dir=C:\OpenVK\chandler\chandler/../extensions/enabled/openvk/storage/, hash=8812b9e766cdd483e9bd454fe8739d655552b767fe113202f62401913a245f21e4d493e9437a171d6bfda7b29466574170f90044433df71bae71b2059ce98bdd, hashT=88
Temp1: C:\Users\<USER>\AppData\Local\Temp\tmp5A10.tmp
Temp2: C:\Users\<USER>\AppData\Local\Temp\tmp5A11.mp4
Source file exists before move
Source file size: 8095070
File copied successfully
Temp file exists after copy
Temp file size: 8095070
Original file removed successfully
ffmpeg thumbnail exit code: 0
Starting video transcode...
ffmpeg : ffmpeg version 2025-05-15-git-12b853530a-full_build-www.gyan.dev Copyright (c) 2000-2025 the FFmpeg developers
At C:\OpenVK\chandler\extensions\available\openvk\Web\Models\shell\processVideo.ps1:77 char:1
+ ffmpeg -y -i $temp -c:v libx264 -q:v 7 -c:a libmp3lame -q:a 4 -tune z ...
+ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : NotSpecified: (ffmpeg version ...mpeg developers:String) [], RemoteException
    + FullyQualifiedErrorId : NativeCommandError
 
  built with gcc 15.1.0 (Rev4, Built by MSYS2 project)
  configuration: --enable-gpl --enable-version3 --enable-static --disable-w32threads --disable-autodetect 
--enable-fontconfig --enable-iconv --enable-gnutls --enable-lcms2 --enable-libxml2 --enable-gmp --enable-bzlib 
--enable-lzma --enable-libsnappy --enable-zlib --enable-librist --enable-libsrt --enable-libssh --enable-libzmq 
--enable-avisynth --enable-libbluray --enable-libcaca --enable-libdvdnav --enable-libdvdread --enable-sdl2 
--enable-libaribb24 --enable-libaribcaption --enable-libdav1d --enable-libdavs2 --enable-libopenjpeg --enable-libquirc 
--enable-libuavs3d --enable-libxevd --enable-libzvbi --enable-libqrencode --enable-librav1e --enable-libsvtav1 
--enable-libvvenc --enable-libwebp --enable-libx264 --enable-libx265 --enable-libxavs2 --enable-libxeve 
--enable-libxvid --enable-libaom --enable-libjxl --enable-libvpx --enable-mediafoundation --enable-libass 
--enable-frei0r --enable-libfreetype --enable-libfribidi --enable-libharfbuzz --enable-liblensfun --enable-libvidstab 
--enable-libvmaf --enable-libzimg --enable-amf --enable-cuda-llvm --enable-cuvid --enable-dxva2 --enable-d3d11va 
--enable-d3d12va --enable-ffnvcodec --enable-libvpl --enable-nvdec --enable-nvenc --enable-vaapi --enable-libshaderc 
--enable-vulkan --enable-libplacebo --enable-opencl --enable-libcdio --enable-libgme --enable-libmodplug 
--enable-libopenmpt --enable-libopencore-amrwb --enable-libmp3lame --enable-libshine --enable-libtheora 
--enable-libtwolame --enable-libvo-amrwbenc --enable-libcodec2 --enable-libilbc --enable-libgsm --enable-liblc3 
--enable-libopencore-amrnb --enable-libopus --enable-libspeex --enable-libvorbis --enable-ladspa --enable-libbs2b 
--enable-libflite --enable-libmysofa --enable-librubberband --enable-libsoxr --enable-chromaprint
  libavutil      60.  2.100 / 60.  2.100
  libavcodec     62.  3.101 / 62.  3.101
  libavformat    62.  0.102 / 62.  0.102
  libavdevice    62.  0.100 / 62.  0.100
  libavfilter    11.  0.100 / 11.  0.100
  libswscale      9.  0.100 /  9.  0.100
  libswresample   6.  0.100 /  6.  0.100
Input #0, mov,mp4,m4a,3gp,3g2,mj2, from 'C:\Users\<USER>\AppData\Local\Temp\tmp5A10.tmp':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2avc1mp41
    encoder         : Lavf61.5.101
  Duration: 00:00:40.87, start: 0.000000, bitrate: 1584 kb/s
  Stream #0:0[0x1](eng): Video: h264 (High) (avc1 / 0x31637661), yuv420p(progressive), 640x480 [SAR 1:1 DAR 4:3], 1447 
kb/s, 29.97 fps, 29.97 tbr, 30k tbn (default)
    Metadata:
      handler_name    : ?Mainconcept Video Media Handler
      vendor_id       : [0][0][0][0]
      encoder         : Lavc61.11.100 libx264
  Stream #0:1[0x2](eng): Audio: aac (LC) (mp4a / 0x6134706D), 48000 Hz, stereo, fltp, 127 kb/s (default)
    Metadata:
      handler_name    : #Mainconcept MP4 Sound Media Handler
      vendor_id       : [0][0][0][0]
Stream mapping:
  Stream #0:0 -> #0:0 (h264 (native) -> h264 (libx264))
  Stream #0:1 -> #0:1 (aac (native) -> mp3 (libmp3lame))
Press [q] to stop, [?] for help
[libx264 @ 00000219c4b4d440] -qscale is ignored, -crf is recommended.
[libx264 @ 00000219c4b4d440] using SAR=1/1
[libx264 @ 00000219c4b4d440] using cpu capabilities: MMX2 SSE2Fast SSSE3 SSE4.2 AVX FMA3 BMI2 AVX2
[libx264 @ 00000219c4b4d440] profile High, level 3.0, 4:2:0, 8-bit
[libx264 @ 00000219c4b4d440] 264 - core 165 r3215 32c3b80 - H.264/MPEG-4 AVC codec - Copyleft 2003-2025 - 
http://www.videolan.org/x264.html - options: cabac=1 ref=3 deblock=1:0:0 analyse=0x3:0x113 me=hex subme=7 psy=1 
psy_rd=1.00:0.00 mixed_ref=1 me_range=16 chroma_me=1 trellis=1 8x8dct=1 cqm=0 deadzone=21,11 fast_pskip=1 
chroma_qp_offset=-2 threads=7 lookahead_threads=7 sliced_threads=1 slices=7 nr=0 decimate=1 interlaced=0 
bluray_compat=0 constrained_intra=0 bframes=0 weightp=2 keyint=250 keyint_min=25 scenecut=40 intra_refresh=0 rc=crf 
mbtree=0 crf=23.0 qcomp=0.60 qpmin=0 qpmax=69 qpstep=4 ip_ratio=1.40 aq=1:1.00
Output #0, mp4, to 'C:\Users\<USER>\AppData\Local\Temp\tmp5A11.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomiso2avc1mp41
    encoder         : Lavf62.0.102
  Stream #0:0(eng): Video: h264 (avc1 / 0x31637661), yuv420p(tv, progressive), 640x480 [SAR 1:1 DAR 4:3], q=2-31, 
29.97 fps, 30k tbn (default)
    Metadata:
      encoder         : Lavc62.3.101 libx264
      handler_name    : ?Mainconcept Video Media Handler
      vendor_id       : [0][0][0][0]
    Side data:
      cpb: bitrate max/min/avg: 0/0/0 buffer size: 0 vbv_delay: N/A
  Stream #0:1(eng): Audio: mp3 (mp4a / 0x6134706D), 48000 Hz, stereo, fltp (default)
    Metadata:
      encoder         : Lavc62.3.101 libmp3lame
      handler_name    : #Mainconcept MP4 Sound Media Handler
      vendor_id       : [0][0][0][0]
frame=  103 fps=0.0 q=28.0 size=     512KiB time=00:00:03.43 bitrate=1220.5kbits/s speed=6.59x elapsed=0:00:00.52    
frame=  194 fps=187 q=28.0 size=    1280KiB time=00:00:06.47 bitrate=1619.9kbits/s speed=6.25x elapsed=0:00:01.03    
frame=  277 fps=179 q=26.0 size=    2048KiB time=00:00:09.24 bitrate=1815.3kbits/s speed=5.96x elapsed=0:00:01.55    
frame=  371 fps=180 q=28.0 size=    2816KiB time=00:00:12.37 bitrate=1863.6kbits/s speed=5.99x elapsed=0:00:02.06    
frame=  459 fps=177 q=27.0 size=    3584KiB time=00:00:15.31 bitrate=1917.1kbits/s speed=5.92x elapsed=0:00:02.58    
frame=  537 fps=174 q=27.0 size=    4352KiB time=00:00:17.91 bitrate=1989.7kbits/s speed= 5.8x elapsed=0:00:03.09    
frame=  613 fps=170 q=28.0 size=    5376KiB time=00:00:20.45 bitrate=2153.2kbits/s speed=5.67x elapsed=0:00:03.60    
frame=  701 fps=170 q=29.0 size=    6144KiB time=00:00:23.39 bitrate=2151.9kbits/s speed=5.67x elapsed=0:00:04.12    
frame=  785 fps=169 q=28.0 size=    7168KiB time=00:00:26.19 bitrate=2241.9kbits/s speed=5.64x elapsed=0:00:04.64    
frame=  868 fps=168 q=27.0 size=    7936KiB time=00:00:28.96 bitrate=2244.7kbits/s speed=5.62x elapsed=0:00:05.15    
frame=  962 fps=170 q=28.0 size=    8704KiB time=00:00:32.09 bitrate=2221.4kbits/s speed=5.66x elapsed=0:00:05.67    
frame= 1071 fps=173 q=26.0 size=    9216KiB time=00:00:35.73 bitrate=2112.7kbits/s speed=5.78x elapsed=0:00:06.18    
frame= 1168 fps=174 q=30.0 size=   10240KiB time=00:00:38.97 bitrate=2152.5kbits/s speed=5.81x elapsed=0:00:06.70    
[out#0/mp4 @ 00000219c4b1b080] video:10703KiB audio:628KiB subtitle:0KiB other streams:0KiB global headers:0KiB muxing 
overhead: 0.360420%
frame= 1225 fps=171 q=23.0 Lsize=   11372KiB time=00:00:40.87 bitrate=2279.2kbits/s speed= 5.7x elapsed=0:00:07.16    
[libx264 @ 00000219c4b4d440] frame I:37    Avg QP:22.02  size: 12100
[libx264 @ 00000219c4b4d440] frame P:1188  Avg QP:25.99  size:  8848
[libx264 @ 00000219c4b4d440] mb I  I16..4: 19.9% 71.0%  9.1%
[libx264 @ 00000219c4b4d440] mb P  I16..4:  4.2% 16.4%  4.7%  P16..4: 26.5% 10.1%  3.8%  0.0%  0.0%    skip:34.3%
[libx264 @ 00000219c4b4d440] 8x8 transform intra:65.5% inter:67.8%
[libx264 @ 00000219c4b4d440] coded y,uvDC,uvAC intra: 58.7% 66.6% 42.7% inter: 17.1% 29.1% 7.0%
[libx264 @ 00000219c4b4d440] i16 v,h,dc,p: 33% 37% 19% 12%
[libx264 @ 00000219c4b4d440] i8 v,h,dc,ddl,ddr,vr,hd,vl,hu: 15% 19% 29%  5%  5%  4%  9%  4% 11%
[libx264 @ 00000219c4b4d440] i4 v,h,dc,ddl,ddr,vr,hd,vl,hu: 12% 27% 22%  5%  6%  3% 11%  3% 10%
[libx264 @ 00000219c4b4d440] i8c dc,h,v,p: 49% 39%  7%  5%
[libx264 @ 00000219c4b4d440] Weighted P-Frames: Y:6.1% UV:4.0%
[libx264 @ 00000219c4b4d440] ref P L0: 59.0%  9.8% 19.1% 11.7%  0.4%
[libx264 @ 00000219c4b4d440] kb/s:2145.00
ffmpeg transcode exit code: 0
Temp2 file exists after transcode
Temp2 file size: 11644940
Moving temp2 to final location: C:\OpenVK\chandler\chandler/../extensions/enabled/openvk/storage/88/8812b9e766cdd483e9bd454fe8739d655552b767fe113202f62401913a245f21e4d493e9437a171d6bfda7b29466574170f90044433df71bae71b2059ce98bdd.mp4
Move operation completed
Final video file exists
Final video file size: 11644940
==== Script Finished ====
