#include <ru>

"__locale" = "su_SU.utf8;su_SU.UTF-8;Rus";
"__WinEncoding" = "Windows-1251";

/* Check for https://www.unicode.org/cldr/charts/latest/supplemental/language_plural_rules.html */

/* Main page */

"home" = "Главная";
"welcome" = "Добро пожаловать";

/* Login */

"log_in" = "Вход";
"password" = "Проходное слово";
"registration" = "Регистрация";
"forgot_password" = "Забыли проходное слово?";

"login_failed" = "Не удалось войти";
"invalid_username_or_password" = "Неверное имя пользователя или пароль. <a href='/restore.pl'>Забыли пароль?</a>";

"failed_to_register" = "Не удалось зарегистрироваться";
"referral_link_invalid" = "Пригласительная ссылка недействительна.";
"registration_disabled" = "Товарищ, регистрация отключена Имперской Канцелярией.";
"user_already_exists" = "Гражданин с таким почтовым ящиком уже существует.";

"access_recovery" = "Восстановление доступа";
"page_access_recovery" = "Восстановить доступ к странице";
"access_recovery_info" = "Забыли пароль? Не волнуйтесь, введите ваши данные и мы отправим вам email с инструкциями по восстановлению аккаунта.";
"access_recovery_info_2" = "Введите ваш новый пароль. Все текущие сеансы будут приостановлены и токены доступа будут аннулированы.";
"reset_password" = "Сбросить пароль";
"2fa_code_2" = "Код двухфакторной аутентификации";

"password_successfully_reset" = "Ваше проходное слово было успешно сброшено.";
"password_reset_email_sent" = "Если вы зарегистрированы, вы получите инструкции на почтовый ящик.";
"password_reset_error" = "Непредвиденная ошибка при сбросе проходного слова.";
"password_reset_rate_limit_error" = "Нельзя делать это так часто, извините.";

"registration_disabled_info" = "Товарищ, регистрация отключена Имперской Канцелярией. При возможности попросите приглашение у вашего знакомого, если он зарегистрирован на этом сайте.";
"registration_closed" = "Регистрация закрыта.";
"invites_you_to" = "<strong>$1</strong> приглашает вас в $2";

"register_meta_desc" = "Зарегистрируйтесь в $1 прямо сейчас!";
"register_referer_meta_title" = "$1 приглашает вас в $2!";
"register_referer_meta_desc" = "Присоединяйтесь к $1 и множеству других граждан в $2!";
"registration_welcome_1" = "- универсальное средство поиска товарищей основанное на структуре ВКонтакте.";
"registration_welcome_2" = "Мы желаем, чтобы друзья, однокурсники, одноклассники, соседи и товарищи всегда могли быть в контакте.";

"users" = "Граждане";
"other_fields" = "Остальное";

/* Profile information */

"select_language" = "Выбрать менталитет";
"edit" = "Корректировать";
"birth_date" = "День рождения";
"registration_date" = "Дата регистрации";
"hometown" = "Родной город";
"this_is_you" = "это Вы";
"edit_page" = "Изменить информацию досье";
"edit_group" = "Изменить информацию клуба";
"change_status" = "изменить ";
"name" = "Имя";
"surname" = "Фамилия";
"gender" = "Пол";
"male" = "мужской";
"female" = "женский";
"description" = "Описание";
"save" = "Сохранить";
"main_information" = "Основная информация";
"nickname" = "Прозвище";
"online" = "Онлайн";
"was_online" = "заходил в орган";
"was_online_m" = "заходил в орган";
"was_online_f" = "заходила в орган";
"all_title" = "Все";
"information" = "Информация";
"status" = "Статус";
"no_information_provided" = "Информация отсутствует.";
"deceased_person" = "Страница покойного человека";
"none" = "отсутствует";
"send" = "Отправить";

"years_zero" = "0 лет";
"years_one" = "1 год";
"years_few" = "$1 года";
"years_many" = "$1 лет";
"years_other" = "$1 лет";

"relationship" = "Семейное положение";

"relationship_0" = "Не выбрано";
"relationship_1" = "Не женат";
"relationship_2" = "Встречаюсь";
"relationship_3" = "Помолвлен";
"relationship_4" = "Женат";
"relationship_5" = "Сожительствую";
"relationship_6" = "Влюблен";
"relationship_7" = "Всё сложно";
"relationship_8" = "В активном поиске";

"politViews" = "Полит. взгляды";

"politViews_0" = "Не выбраны";
"politViews_1" = "Не в партии";
"politViews_2" = "Коммунистические";
"politViews_3" = "Социалистические";
"politViews_4" = "Центристские";
"politViews_5" = "Антисоветские";
"politViews_6" = "Консервативные";
"politViews_7" = "Контрреволюционные";
"politViews_8" = "Ультраконсервативные";
"politViews_9" = "Либертарианские";

"contact_information" = "Контактная информация";

"email" = "Почтовый ящик";
"phone" = "Стационарный телефон";
"telegram" = "Telegram";
"personal_website" = "Личная визитка";
"city" = "Город";
"address" = "Адрес";

"personal_information" = "Личность";

"interests" = "Интересы";
"favorite_music" = "Любимые звукозаписи";
"favorite_films" = "Любимые киноленты";
"favorite_shows" = "Любимые программы";
"favorite_books" = "Любимые книги";
"favorite_quotes" = "Любимые цитаты";
"information_about" = "О себе";

"updated_at" = "Обновлено $1";

"user_banned" = "Органу управления пришлось отправить <b>$1</b> под стражу.";
"user_banned_comment" = "Комментарий милиции:";

/* Wall */

"feed" = "Новостная газета";

"post_writes_m" = "написал";
"post_writes_f" = "написала";
"post_writes_g" = "опубликовали";
"wall" = "Доска";
"post" = "Запись";
"write" = "Написать";
"publish" = "Опубликовать";
"delete" = "Удалить";
"comments" = "Отзывы";
"share" = "Поделиться";
"pin" = "Закрепить";
"unpin" = "Открепить";
"pinned" = "закреплено";
"comments_tip" = "Гражданин, будьте первым, кто напишет отзыв!";
"your_comment" = "Ваш отзыв";
"auditory" = "Зрители";
"in_wall" = "в досье";
"in_group" = "в собрание";
"shown" = "Показано";
"x_out_of" = "$1 из";
"wall_zero" = "нет записей";
"wall_one" = "единственная запись";
"wall_few" = "$1 записи";
"wall_many" = "$1 записей";
"wall_other" = "$1 записей";
"publish_post" = "Добавить запись";
"view_other_comments" = "Посмотреть остальные комментарии";

"no_comments" = "Отзывы отсутствуют";

"all_news" = "Все новостные газеты";
"posts_per_page" = "Количество записей в досье";

"attachment" = "Вложение";
"post_as_group" = "От имени клуба";
"comment_as_group" = "От имени клуба";
"add_signature" = "Подпись участника клуба";
"contains_nsfw" = "Содержит запрещённый контент";
"nsfw_warning" = "Товарищ, данная запись не прошла цензуру органом записи, мы не рекомендуем данную запись просматривать.";
"report" = "Пожаловаться";
"attach" = "Прикрепить";
"attach_photo" = "Прикрепить картинку";
"attach_video" = "Прикрепить киноленту";
"draw_graffiti" = "Нарисовать иллюстрацию";
"no_posts_abstract" = "Товарищ, здесь пока никто ничего не оставил.";
"attach_no_longer_available" = "Этот материал больше не подлежит к просмотру.";
"open_post" = "Открыть запись";
"version_incompatibility" = "Товарищ, органу управления не удалось достать из библиотеки данный материал. Возможно, он был порван или бибилотека не совместима с версией OpenVK";

"reply" = "Ответить";

/* Friends */

"friends" = "Товарищи";
"followers" = "Подписчики";
"follower" = "Подписчик";
"friends_add" = "Взять в товарищи";
"friends_delete" = "Отвергнуть товарища";
"friends_reject" = "Порвать приглашение в товарищи";
"friends_accept" = "Прочитать приглашение в товарищи";
"send_message" = "Отправить телеграмму";
"incoming_req" = "Подписчики";
"outcoming_req" = "Заявки";
"req" = "Заявки";

"friends_zero" = "Ни одного товарища";
"friends_one" = "$1 товарищ";
"friends_few" = "$1 товарища";
"friends_many" = "$1 товарищей";
"friends_other" = "$1 друзей";

"followers_zero" = "Ни одного подписчика";
"followers_one" = "$1 подписчик";
"followers_few" = "$1 подписчика";
"followers_many" = "$1 подписчиков";
"followers_other" = "$1 подписчиков";

"subscriptions_zero" = "Ни одной подписки";
"subscriptions_one" = "$1 подписка";
"subscriptions_few" = "$1 подписки";
"subscriptions_many" = "$1 подписок";
"subscriptions_other" = "$1 подписок";

/* Group */

"name_group" = "Название";
"subscribe" = "Подписаться";
"unsubscribe" = "Отписаться";
"subscriptions" = "Подписки";
"join_community" = "Вступить в клуб";
"leave_community" = "Выйти из клуба";
"min_6_community" = "Название должно быть не менее 6 символов";
"participants" = "Участники";
"groups" = "Клубы";
"meetings" = "Встречи";
"create_group" = "Создать клуб";
"group_managers" = "Руководство";
"group_type" = "Тип группы";
"group_type_open" = "Это открытая группа. В неё может вступить любой желающий.";
"group_type_closed" = "Это закрытая группа. Для вступления необходимо подавать заявку.";
"creator" = "Создатель";
"administrators" = "Администраторы";
"add_to_left_menu" = "Добавить в левое меню";
"remove_from_left_menu" = "Удалить из левого меню";
"all_followers" = "Все подписчики";
"only_administrators" = "Только администраторы";
"website" = "Сайт";
"managed" = "Управляемые";
"size" = "Размер";

"administrators_one" = "$1 администратор";
"administrators_few" = "$1 администратора";
"administrators_other" = "$1 администраторов";

"role" = "Роль";
"administrator" = "Администратор";
"promote_to_admin" = "Повысить до администратора";
"promote_to_owner" = "Назначить владельцем";
"devote" = "Разжаловать";
"set_comment" = "Изменить комментарий";
"hidden_yes" = "Скрыт: Да";
"hidden_no" = "Скрыт: Нет";
"group_allow_post_for_everyone" = "Разрешить публиковать записи всем";
"group_hide_from_global_feed" = "Не отображать записи в публичной доске";
"statistics" = "Статистика";
"group_administrators_list" = "Список админов";
"group_display_only_creator" = "Отображать только создателя группы";
"group_display_all_administrators" = "Отображать всех администраторов";
"group_dont_display_administrators_list" = "Ничего не отображать";

"group_changeowner_modal_title" = "Передача прав владельца";
"group_changeowner_modal_text" = "Внимание! Вы передаёте права владельца пользователю $1. Это действие необратимо. После передачи вы останетесь адмиинстратором, но сможете легко перестать им быть.";
"group_owner_setted" = "Новый владелец ($1) успешно назначен в сообщество $2. Вам выданы права администратора в сообществе. Если Вы хотите вернуть роль владельца, обратитесь в <a href='/support?act=new'>техническую поддержку сайта</a>.";

"participants_zero" = "Ни одного участника";
"participants_one" = "Один участник";
"participants_few" = "$1 участника";
"participants_many" = "$1 участников";
"participants_other" = "$1 участников";

"groups_zero" = "Ни одной группы";
"groups_one" = "Одна группа";
"groups_few" = "$1 группы";
"groups_many" = "$1 групп";
"groups_other" = "$1 групп";

"meetings_zero" = "Ни одной встречи";
"meetings_one" = "Одна встреча";
"meetings_few" = "$1 встречи";
"meetings_many" = "$1 встреч";
"meetings_other" = "$1 встреч";

/* Albums */

"create" = "Создать";
"albums" = "Альбомы";
"create_album" = "Создать альбом с картинками";
"edit_album" = "Поправить альбом с картинками";
"creating_album" = "Создание альбома с картинками";
"upload_photo" = "Отправить картинку";
"photo" = "Картинка";
"upload_button" = "Картинка";

"open_original" = "Посмотреть оригинал картинки";

"avatar_album" = "Картинки из досье";
"wall_album" = "Картинки с доски";

"albums_zero" = "Ни одного альбома с картинками";
"albums_one" = "Один альбом с картинками";
"albums_few" = "$1 альбома с картинками";
"albums_many" = "$1 альбомов с картинками";
"albums_other" = "$1 альбомов с картинками";
"albums_list_zero" = "У Вас нет ни одного альбома с картинками";
"albums_list_one" = "У Вас один альбом с картинками";
"albums_list_few" = "У Вас $1 альбома с картинками";
"albums_list_many" = "У Вас $1 альбомов с картинками";
"albums_list_other" = "У Вас $1 альбомов с картинками";

"add_image" = "Поставить фотокарточку";
"add_image_group" = "Загрузка фотокарточки";
"upload_new_picture" = "Загрузить новую фотокарточку";
"uploading_new_image" = "Загрузка новой фотокарточки";
"friends_avatar" = "Знакомым будет проще узнать Вас, если вы загрузите свою настоящую фотокарточку.";
"groups_avatar" = "Хорошая фотокарточка сделает Ваше собрание более узнаваемым.";
"formats_avatar" = "Вы можете загрузить плёнку в формате JPG, GIF или PNG.";
"troubles_avatar" = "Если возникают проблемы с прикреплением, попробуйте выбрать фотокарточку меньшего размера.";
"webcam_avatar" = "Если ваш компьютер оснащён фотокамерой, Вы можете <a href='javascript:'>сделать быструю фотокарточку »</a>";

"update_avatar_notification" = "Фотокарточка досье обновлена";
"update_avatar_description" = "Нажмите сюда, чтобы перейти к рассмотрению";

"deleting_avatar" = "Удаление фотокарточки";
"deleting_avatar_sure" = "Вы действительно хотите удалить фотокарточку?";

"deleted_avatar_notification" = "Фотокарточка успешно откреплена";

"save_changes" = "Сохранить изменения";

"upd_m" = "обновил фотокарточку на своём досье";
"upd_f" = "обновила фотокарточку на своём досье";
"upd_g" = "обновило фотокарточку собрания";


/* Notes */

"notes" = "Черновики";
"note" = "Черновик";
"name_note" = "Название";
"text_note" = "Содержание";
"create_note" = "Написать новый черновик";
"actions" = "Действия";

"notes_zero" = "Ни одного черновика";
"notes_one" = "Один черновик";
"notes_few" = "$1 черновика";
"notes_many" = "$1 черновиков";
"notes_other" = "$1 черновиков";

/* Menus */

"edit_button" = "корр.";
"my_page" = "Моё Досье";
"my_friends" = "Мои Товарищи";
"my_photos" = "Мои Фотокартинки";
"my_videos" = "Мои Киноленты";
"my_messages" = "Мои Телеграмы";
"my_notes" = "Мои Записки";
"my_groups" = "Мои Клубы";
"my_feed" = "Мое Информбюро";
"my_feedback" = "Мои Сводки";
"my_settings" = "Мои Настройки";
"bug_tracker" = "Доска задач";

"menu_login" = "Вход";
"menu_registration" = "Регистрация";
"menu_help" = "Справка";

"menu_logout" = "Эмигрировать";
"menu_support" = "Справочная";

"header_home" = "главная";
"header_groups" = "клубы";
"header_people" = "граждане";
"header_invite" = "пригласить";
"header_help" = "справка";
"header_log_out" = "эмиграция";
"header_search" = "Розыск";

"header_login" = "вход";
"header_registration" = "регистрация";

"left_menu_donate" = "Поддержать";

"footer_rules" = "правила";
"footer_blog" = "доска новостей";
"footer_help" = "помощь";
"footer_developers" = "госслужащим";
"footer_choose_language" = "выбрать язык";
"footer_privacy" = "секретность";

/* Settings */

"main" = "Основное";
"contacts" = "Контакты";
"avatar" = "Картинка";
"privacy" = "Секретность";
"interface" = "Внешний вид";

"profile_picture" = "Изображение страницы";

"picture" = "Изображение";

"change_password" = "Изменить кодовое слово";
"old_password" = "Старое кодовое слово";
"new_password" = "Новое кодовое слово";
"repeat_password" = "Повторите кодовое слово";

"avatars_style" = "Отображение картинок";
"style" = "Стиль";

"default" = "По умолчанию";
"cut" = "Обрезка";
"round_avatars" = "Круглая картинка";

"search_for_groups" = "Розыск собраний";
"search_for_users" = "Розыск граждан";
"search_for_posts" = "Розыск записей";
"search_for_comments" = "Розыск отзывов";
"search_for_videos" = "Розыск кинолент";
"search_for_apps" = "Розыск приложений";
"search_for_notes" = "Розыск записок";
"search_for_audios" = "Розыск аудио";

"search_button" = "Найти";
"search_placeholder" = "Начните вводить любое имя, название или слово";
"results_zero" = "Ни одного результата";
"results_one" = "Один результат";
"results_few" = "$1 результата";
"results_many" = "$1 результатов";
"results_other" = "$1 результатов";

"privacy_setting_access_page" = "Кому из граждан видно моё досье";
"privacy_setting_read_info" = "Кому видно основную информацию моей страницы";
"privacy_setting_see_groups" = "Кому разрешено просматривать мои картинки и встречи";
"privacy_setting_see_photos" = "Кому разрешено просматривать мои картинки";
"privacy_setting_see_videos" = "Кому разрешено смотреть мои киноленты";
"privacy_setting_see_notes" = "Кому разрешено просматривать мои черновики";
"privacy_setting_see_friends" = "Кому видно моих товарщией";
"privacy_setting_add_to_friends" = "Кто может звать меня товарищем";
"privacy_setting_write_wall" = "Кто может оставлять записи на доске";
"privacy_setting_write_messages" = "Кто может отправлять мне телеграммы";
"privacy_value_anybody" = "Все граждане";
"privacy_value_anybody_dative" = "Всем гражданинам";
"privacy_value_users" = "Участникам органа OpenVK";
"privacy_value_friends" = "Товарищи";
"privacy_value_friends_dative" = "Товарищам";
"privacy_value_only_me" = "Только я и КГБ";
"privacy_value_only_me_dative" = "Только мне и КГБ";
"privacy_value_nobody" = "Никто";

"your_email_address" = "Адрес Вашего почтового ящика";
"your_page_address" = "Адрес Вашего досье";
"page_address" = "Адрес досье";
"current_email_address" = "Текущий адрес";
"page_id" = "Номер досье";
"you_can_also" = "Вы также можете";
"delete_your_page" = "порвать своё досье";
"delete_album" = "выкинуть свой альбом с картинками";

"ui_settings_interface" = "Интерфейс";
"ui_settings_sidebar" = "Левое меню";
"ui_settings_rating" = "Рейтинг";
"ui_settings_rating_show" = "Показывать";
"ui_settings_rating_hide" = "Скрывать";

"additional_links" = "Дополнительные ссылки";
"ad_poster" = "Рекламный плакат";
/* Two-factor authentication */

"two_factor_authentication" = "Двухфакторная аутентификация";
"two_factor_authentication_disabled" = "Обеспечивает надежную защиту от взлома: для входа на страницу необходимо ввести код, полученный в приложении 2FA.";
"two_factor_authentication_enabled" = "Двухфакторная аутентификация включена. Ваша страница защищена.";
"two_factor_authentication_login" = "У вас включена двухфакторная аутентификация. Для входа введите код полученный в приложении.";

"two_factor_authentication_settings_1" = "Двухфакторную аутентификацию через TOTP можно использовать даже без интернета. Для этого вам понадобится приложение для генерации кодов. Например, <b>Google Authenticator</b> для Android и iOS или свободные <b>Aegis и andOTP</b> для Android. Убедитесь, что на телефоне точно установлена дата и время.";
"two_factor_authentication_settings_2" = "Используя приложение для двухфакторной аутентификации, отсканируйте приведенный ниже QR-код:";
"two_factor_authentication_settings_3" = "или вручную введите секретный ключ: <b>$1</b>.";
"two_factor_authentication_settings_4" = "Теперь введите код, который вам предоставило приложение, и пароль от вашей страницы, чтобы мы могли подтвердить, что вы действительно вы.";

"connect" = "Подключить";
"enable" = "Включить";
"disable" = "Отключить";
"code" = "Код";
"2fa_code" = "Код 2FA";

"incorrect_password" = "Неверный пароль";
"incorrect_code" = "Неверный код";
"incorrect_2fa_code" = "Неверный код двухфакторной аутентификации";
"two_factor_authentication_enabled_message" = "Двухфакторная аутентификация включена";
"two_factor_authentication_enabled_message_description" = "Вашу страницу стало труднее взломать. Рекомендуем вам скачать <a href='javascript:viewBackupCodes()'>резервные коды</a>";
"two_factor_authentication_disabled_message" = "Двухфакторная аутентификация отключена";

"view_backup_codes" = "Посмотреть резервные коды";
"backup_codes" = "Резервные коды для подтверждения входа";
"two_factor_authentication_backup_codes_1" = "Резервные коды позволяют подтверждать вход, когда у вас нет доступа к телефону, например, в путешествии.";
"two_factor_authentication_backup_codes_2" = "У вас есть ещё <b>10 кодов</b>, каждым кодом можно воспользоваться только один раз. Распечатайте их, уберите в надежное место и используйте, когда потребуются коды для подтверждения входа.";
"two_factor_authentication_backup_codes_3" = "Вы можете получить новые коды, если они заканчиваются. Действительны только последние созданные резервные коды.";

/* Sorting */

"sort_randomly" = "Сортировать случайно";
"sort_up" = "Сортировать по дате создания вверх";
"sort_down" = "Сортировать по дате создания вниз";

/* Videos */

"videos" = "Киноленты";
"video" = "Кинолента";
"upload_video" = "Отправить киноленту";
"video_uploaded" = "Отправлено";
"video_updated" = "Изменено";
"video_link_to_yt" = "Номер на YouTube";

"info_name" = "Название";
"info_description" = "Описание";
"info_uploaded_by" = "Отправил";
"info_upload_date" = "Дата отправки киноленты";

"videos_zero" = "Ни однойкиноленты";
"videos_one" = "Одна кинолента";
"videos_few" = "$1 киноленты";
"videos_many" = "$1 кинолент";
"videos_other" = "$1 кинолент";

/* Notifications */

"feedback" = "Ответы";
"unread" = "Непрочитанное";
"archive" = "Архив";

"notifications_like" = "$1 оценил вашу $2запись$3 от $4";
"notifications_repost" = "$1 поделился(-лась) вашей $2записью$3 от $4";
"notifications_comment_under" = "$1 оставил(-ла) комментарий под $2";
"notifications_under_note" = "вашей $3заметкой$4";
"notifications_under_photo" = "вашим $3фото$4";
"notifications_under_post" = "вашей $3записью$4 от $5";
"notifications_under_video" = "вашей $3кинолентой$4";
"notifications_post" = "$1 написал(-ла) $2запись$3 на вашей стене: $4";
"notifications_appoint" = "$1 назвачил вас руководителем сообщества $2";

"nt_liked_yours" = "понравился ваш";
"nt_shared_yours" = "поделился(-ась) вашим";
"nt_commented_yours" = "оставил(а) комментарий под";
"nt_written_on_your_wall" = "написал(а) на вашей стене";
"nt_made_you_admin" = "назначил(а) вас руководителем сообщества";

"nt_from" = "от";
"nt_yours_adjective" = "вашим";
"nt_yours_feminitive_adjective" = "вашей";
"nt_post_nominative" = "запись";
"nt_post_instrumental" = "записью";
"nt_note_instrumental" = "черновиком";
"nt_photo_instrumental" = "картинкой";
"nt_topic_instrumental" = "темой";

/* Time */

"time_at_sp" = " в ";
"time_just_now" = "только что";
"time_exactly_five_minutes_ago" = "ровно 5 минут назад";
"time_minutes_ago" = "$1 минут назад";
"time_today" = "сегодня";
"time_yesterday" = "вчера";

"points" = "Советские рубли";
"points_count" = "советских рублей";
"on_your_account" = "на вашем счету";

"vouchers" = "Лотерейные карточки";
"have_voucher" = "Есть лотерейная карточка";
"voucher_token" = "Код лотерейной карточки";
"voucher_activators" = "Воспользовавшиеся";
"voucher_explanation" = "Товарищ, введите сюда кодовый номер лотерейной карточки. Орган указывает его в вашем чеке или телеграмме.";
"voucher_explanation_ex" = "Мы забираем лотерейные карточки, дубликаты отклоняются.";
"invalid_voucher" = "Такуя лоетрейную карточку мы уже приняли";
"voucher_bad" = "Товарищ, мы уже принимали такую карточку, либо её срок истёк.";
"voucher_good" = "Лотерейная карточка активирован";
"voucher_redeemed" = "Товарищ, благодарим за отправку карточки! Мы уже начислили по ней рубли, но вы больше не сможете их повторно получить по той карточке.";
"redeem" = "Активировать лотерейную карточку";
"deactivate" = "Порвать";
"usages_total" = "Количество использований";
"usages_left" = "Осталось использований";

"points_transfer_dialog_header_1" = "Вы можете отправить в подарок или передать часть рублей другому гражданину.";
"points_transfer_dialog_header_2" = "Ваш текущий баланс:";

"points_amount_one" = "1 рубль";
"points_amount_few" = "$1 рубля";
"points_amount_many" = "$1 рублей";
"points_amount_other" = "$1 рублей";

"transfer_poins" = "Передача рублей";
"transfer_poins_button" = "Передать рубли";
"also_you_can_transfer_points" = "Также вы можете <a href=\"javascript:showCoinsTransferDialog($1, '$2')\">передать рубли</a> другому гражданину.";

"transferred_to_you" = "передал вам";

"receiver_address" = "Адрес досье получателя";
"coins_count" = "Количество рублей";
"message" = "Сообщение";

"failed_to_tranfer_points" = "Не удалось передать рубли";

"points_transfer_successful" = "Вы успешно передали <b>$1 <a href=\"$2\">$3</a></b>.";
"not_all_information_has_been_entered" = "Введена не вся информация.";
"negative_transfer_value" = "Орган не подразумевает нарушения закона, чтобы украсть чужие деньги.";
"message_is_too_long" = "Сообщение слишком длинное.";
"receiver_not_found" = "Гражданин не найден.";
"you_dont_have_enough_points" = "У вас недостаточно рублей.";

/* Gifts */

"gift" = "Подарок";
"gifts" = "Подарки";
"gifts_zero" = "Нет подарков";
"gifts_one" = "Один подарок";
"gifts_few" = "$1 подарка";
"gifts_many" = "$1 подарков";
"gifts_other" = "$1 подарков";
"gifts_left" = "Подарков осталось: $1";
"gifts_left_one" = "Один подарок остался";
"gifts_left_few" = "$1 подарка осталось";
"gifts_left_many" = "$1 подарков осталось";
"gifts_left_other" = "$1 подарков осталось";

"send_gift" = "Отправить подарок";

"gift_select" = "Выбрать подарок";
"collections" = "Коллекции";
"confirm" = "Подтверждение";
"as_anonymous" = "Анонимно";
"gift_your_message" = "Ваше сообщение";

"free_gift" = "Бесплатно";
"coins" = "Голоса";
"coins_zero" = "0 рублей";
"coins_one" = "Один рубль";
"coins_few" = "$1 рубля";
"coins_many" = "$1 рублей";
"coins_other" = "$1 рублей";

"users_gifts" = "Подарки";

/* Support */

"increase_rating" = "Увеличить рейтинг";
"increase_rating_button" = "Увеличить";
"to_whom" = "Кому";
"increase_by" = "Увеличить на";
"price" = "Ценник";

"you_have_unused_votes" = "У Вас $1 неиспользованных рубля на кошельке.";
"apply_voucher" = "Применить купон";

"failed_to_increase_rating" = "Не удалось увеличить рейтинг";
"rating_increase_successful" = "Вы успешно увеличили рейтинг <b><a href=\"$1\">$2</a></b> на <b>$3%</b>.";
"negative_rating_value" = "Орган не может украсть баллы рейтинга у другого гражданина, так не положено.";

"increased_your_rating_by" = "увеличил ваш рейтинг на";

"support_opened" = "Открытые";
"support_answered" = "С ответом";
"support_closed" = "Закрытые";
"support_ticket" = "Обращение";
"support_tickets" = "Обращения";
"support_status_0" = "Вопрос на рассмотрении";
"support_status_1" = "Есть ответ";
"support_status_2" = "Закрыто";
"support_greeting_hi" = "Здравствуйте, $1!";
"support_greeting_regards" = "С уважением,<br/>команда поддержки $1.";

"support_faq" = "Часто задаваемые вопросы";
"support_list" = "Список обращений";
"support_new" = "Новое обращение";

"support_faq_title" = "Для кого этот сайт?";
"support_faq_content" = "Сайт предназначен для поиска друзей и знакомых, а также для просмотра данных пользователя. Это как справочник города, с помощью которого люди могут быстро найти актуальную информацию о человеке.";

"support_new_title" = "Введите тему вашего обращения";
"support_new_content" = "Опишите проблему или предложение";

"support_rate_good_answer" = "Подарить конфеты";
"support_rate_bad_answer" = "Закатить скандал";
"support_good_answer_user" = "Вы подарили конфеты сотруднику справочной.";
"support_bad_answer_user" = "Вы закатили скандал сотруднику справочной.";
"support_good_answer_agent" = "Гражданин подарил конфеты сотруднику справочной";
"support_bad_answer_agent" = "Гражданин закатил скандал сотруднику справочной";
"support_rated_good" = "Вы оставили положительный отзыв об ответе.";
"support_rated_bad" = "Вы оставили негативный отзыв об ответе.";
"wrong_parameters" = "Неверные параметры запроса.";

"fast_answers" = "Быстрые ответы";

"comment" = "Отзыв";
"sender" = "Отправитель";

"author" = "Автор";

"you_have_not_entered_text" = "Вы не ввели текст";
"you_have_not_entered_name_or_text" = "Вы не ввели имя или текст";

"ticket_changed" = "Тикет изменён";
"ticket_changed_comment" = "Изменения вступят силу через несколько секунд.";

/* Invite */

"invite" = "Позвать";
"you_can_invite" = "Товарищ, вы можете позвать своих товарщией в орган с помощью индивидуального номера ссылки:";
"you_can_invite_2" = "Приложите номер ссылки к вашей телеграмме. Гражданин вступит в орган и вы сразу станете товарищами.";


/* Banned */

"banned_title" = "Вам бан";
"banned_header" = "Вы были отправлены в тюрьму";
"banned_alt" = "Гражданин был отправлен в тюрьму.";
"banned_1" = "Извините, <b>$1</b>, но вы были отправлены в тюрьму.";
"banned_2" = "А причина этому проста: <b>$1</b>. Органу в этот раз пришлось отправить вас под стражу навсегда.";
"banned_3" = "Вы всё ещё можете <a href=\"/support?act=new\">написать в Справочную</a>, если считаете что произошла ошибка или <a href=\"/logout?hash=$1\">эмигрировать</a>.";

/* Discussions */

"discussions" = "Обсуждения";

"messages_one" = "Одна телеграмма";
"messages_few" = "$1 телеграмм";
"messages_many" = "$1 телеграмм";
"messages_other" = "$1 телеграмм";

"topic_messages_count_zero" = "В теме нет телеграмм";
"topic_messages_count_one" = "В теме одна телеграмма";
"topic_messages_count_few" = "В теме $1 телеграмм";
"topic_messages_count_many" = "В теме $1 телеграмм";
"topic_messages_count_other" = "В теме $1 телеграмм";

"replied" = "ответил";
"create_topic" = "Создать тему";

"new_topic" = "Новая тема";
"title" = "Заголовок";
"text" = "Текст";

"view_topic" = "Просмотр темы";
"edit_topic_action" = "Редактировать тему";
"edit_topic" = "Редактирование темы";
"topic_settings" = "Настройки темы";
"pin_topic" = "Закрепить тему";
"close_topic" = "Закрыть тему";
"delete_topic" = "Удалить тему";

"topics_one" = "Одна тема";
"topics_few" = "$1 темы";
"topics_many" = "$1 тема";
"topics_other" = "$1 тем";

"created" = "Создано";

"everyone_can_create_topics" = "Все могут создавать темы";
"display_list_of_topics_above_wall" = "Отображать список тем над стеной";

"topic_changes_saved_comment" = "Обновлённый заголовок и настройки появятся на странице с темой.";

"failed_to_create_topic" = "Не удалось создать тему";
"failed_to_change_topic" = "Не удалось изменить тему";
"no_title_specified" = "Заголовок не указан.";

/* Errors */

"error_1" = "Некорректный запрос";
"error_2" = "Неверный логин или пароль";
"error_3" = "Не авторизован";
"error_4" = "Пользователь не существует";
"information_-1" = "Операция выполнена успешно";
"information_-2" = "Вход выполнен успешно";

"no_data" = "Нет данных";
"no_data_description" = "Товарищ, на этой доске нету записей.";

"error" = "Ошибка";
"error_shorturl" = "Данный короткий адрес уже занят.";
"error_segmentation" = "Ошибка сегментации";
"error_upload_failed" = "Не удалось загрузить фото";
"error_old_password" = "Старый пароль не совпадает";
"error_new_password" = "Новые пароли не совпадает";
"error_shorturl_incorrect" = "Короткий адрес имеет некорректный формат.";
"error_repost_fail" = "Не удалось поделиться записью";

"forbidden" = "Ошибка доступа";
"forbidden_comment" = "Настройки приватности этого пользователя не разрешают вам смотреть на его страницу.";

"changes_saved" = "Изменения сохранены";
"changes_saved_comment" = "Товарищ, обоновлённые данные появятся на вашем досье.";

"photo_saved" = "Картинка сохранена";
"photo_saved_comment" = "Товарищ, орган одобрил новую картинку в вашем досье.";

"shared_succ" = "Запись появится на вашей доске. Нажмите на уведомление, чтобы перейти к своей стене.";

"invalid_email_address" = "Неверный адрес почтового ящика";
"invalid_email_address_comment" = "Товарищ, нам не удалось найти ваш почтовый ящик.";

"invalid_telegram_name" = "Неверное имя Telegram аккаунта";
"invalid_telegram_name_comment" = "Вы ввели неверное имя аккаунта Telegram.";

"invalid_birth_date" = "Неверная дата рождения";
"invalid_birth_date_comment" = "Дата рождения, которую вы ввели, не является корректной.";

"token_manipulation_error" = "Ошибка манипулирования токеном";
"token_manipulation_error_comment" = "Токен недействителен или истёк";

"profile_changed" = "Досье изменено";
"profile_changed_comment" = "Товарищ, орган одобрил изменение вашего досье.";
"profile_not_found" = "Гражданин не найден.";

"suspicious_registration_attempt" = "Подозрительная попытка регистрации в орган";
"suspicious_registration_attempt_comment" = "Товарищ, а вы откуда к нам пришли? Вы очень подозрительно себя ведёте.";

"rate_limit_error" = "Чумба, ты совсем ёбнутый?";
"rate_limit_error_comment" = "Сходи к мозгоправу, попей колёсики. В $1 нельзя вбрасывать щитпосты так часто. Код исключения: $2.";

"not_enough_permissions" = "Недостаточно прав";
"not_enough_permissions_comment" = "У вас недостаточно прав чтобы выполнять это действие.";

"login_required_error" = "Недостаточно прав";
"login_required_error_comment" = "Чтобы просматривать эту страницу, нужно зайти на сайт.";

"captcha_error" = "Неправильно введены символы";
"captcha_error_comment" = "Пожалуйста, убедитесь, что вы правильно заполнили поле с капчей.";

/* Admin actions */

"login_as" = "Войти как $1";
"manage_user_action" = "Управление гражданином";
"manage_group_action" = "Управление клубом";
"ban_user_action" = "Заблокировать гражданина";
"warn_user_action" = "Предупредить гражданина";

/* Paginator (deprecated) */

"paginator_back" = "Назад";
"paginator_page" = "Страница $1";
"paginator_next" = "Дальше";

/* About */

"about_openvk" = "Об органе OpenVK";
"footer_about_instance" = "о стране";
"about_this_instance" = "Об этой стране";
"rules" = "Правила";
"most_popular_groups" = "Самые популярные клубы";
"on_this_instance_are" = "На этой стране";

"about_users_one" = "<b>1</b> гражданин";
"about_users_few" = "<b>$1</b> гражданина";
"about_users_many" = "<b>$1</b> гражданинов";
"about_users_other" = "<b>$1</b> гражданинов";

"about_online_users_one" = "<b>1</b> гражданин в сети";
"about_online_users_few" = "<b>$1</b> гражданина в сети";
"about_online_users_many" = "<b>$1</b> граждан в сети";
"about_online_users_other" = "<b>$1</b> граждан в сети";

"about_active_users_one" = "<b>1</b> активный гражданин";
"about_active_users_few" = "<b>$1</b> активных граждан";
"about_active_users_many" = "<b>$1</b> активных граждан";
"about_active_users_other" = "<b>$1</b> активных граждан";

"about_groups_one" = "<b>1</b> клуб";
"about_groups_few" = "<b>$1</b> клубы";
"about_groups_many" = "<b>$1</b> клубов";
"about_groups_other" = "<b>$1</b> клубов";

"about_wall_posts_one" = "<b>1</b> заметка на досках";
"about_wall_posts_few" = "<b>$1</b> заметки на досках";
"about_wall_posts_many" = "<b>$1</b> заметки на досках";
"about_wall_posts_other" = "<b>$1</b> заметки на досках";

/* Dialogs */

"ok" = "ОК";
"yes" = "Да";
"no" = "Нет";
"cancel" = "Отмена";
"edit_action" = "Изменить";
"transfer" = "Передать";
"close" = "Закрыть";

"warning" = "Внимание";
"question_confirm" = "Товарищ, будьте внимательны с выбором. Вы согласны с вашим выбором? Отменить не представляется возможным.";

/* User alerts */
"apply_style_for_this_device" = "Применить стиль только для этой ЭВМ";

"user_alert_scam" = "Органу управления было дозволено, что данный гражданин обманывает товарищей на денежные средства. Будьте осторожны при разговоре с ним.";

"ec_header" = "Подтверждение регистрации прописки";
"ec_title" = "Спасибо!";
"ec_1" = "<b>$1</b>, на ваш почтовый ящик должно придти письмо с подтверждением регистрации.";
"ec_2" = "Если по каким-то причинам вам не пришло письмо, то проверьте мусорный бак. Если письма не окажется и там, то вы можете переотправить письмо.";
"ec_resend" = "Переотправить письмо";

"email_sent" = "Письмо было успешно отправлено.";
"email_sent_desc" = "Если ваш почтовый ящик существует, вы получите письмо.";
"email_error" = "Непредвиденная ошибка при отправке письма.";
"email_rate_limit_error" = "Нельзя делать это так часто, извините.";

"email_verify_success" = "Ваша регистрация была подтверждена. Приятного времяпрепровождения!";

"you_still_have_x_points" = "У Вас <b>$1</b> неиспользованных совестких рублей.";
"top_up_your_account" = "Пополнить баланс";

"transfer_trough_ton" = "Пополнить с помощью ТОН";
"transfer_ton_contents" = "Вы можете пополнить ваш баланс с помощью криптовалюты ТОН. Достаточно отсканировать КуАр-код приложением Tonkeeper, или вручную отправить ТОН по реквизитам. В течении нескольких минут вам придут определенное количество рублей.";
"transfer_ton_address" = "<b>Адрес кошелька:</b> $1<br/><b>Содержание телеграммы:</b> $2";
"transfer_ton_currency_per_ton" = "$1 TON";

"about_links" = "Ссылки";
"instance_links" = "Ссылки страны:";

"my_apps" = "Досуг и отдых";

/* Search */

"s_people" = "Граждане";
"s_groups" = "Собрания";
"s_events" = "События";
"s_apps" = "Приложения";
"s_questions" = "Вопросы";
"s_notes" = "Заметки";
"s_themes" = "Темы";
"s_posts" = "Записи";
"s_comments" = "Отзывы";
"s_videos" = "Киноленты";
"s_audios" = "Аудио";
"s_by_people" = "по гражданам";
"s_by_groups" = "по собраниям";
"s_by_posts" = "по записям";
"s_by_comments" = "по отзывам";
"s_by_videos" = "по кинолентам";
"s_by_apps" = "по приложениям";
"s_by_audios" = "по музыке";

"s_order_by" = "Расположение";

"s_order_by_id" = "По номерам";
"s_order_by_name" = "По имени";
"s_order_by_random" = "Наобум";
"s_order_by_rating" = "По соц. рейтингу";
"s_order_invert" = "Отразить";

"s_by_date" = "По дате";
"s_registered_before" = "Зарегистрирован до";
"s_registered_after" = "Зарегистрирован после";
"s_date_before" = "До";
"s_date_after" = "После";

"s_main" = "Паспорт";

"s_now_on_site" = "cейчас доступен";
"s_with_photo" = "с фотокарточкой";
"s_only_in_names" = "только в паспорте";

"s_any" = "любой";
"reset" = "Сброс";

"closed_group_post" = "Эта запись из закрытого собрания";
"deleted_target_comment" = "Этот отзыв надлежит к удалённой записи";