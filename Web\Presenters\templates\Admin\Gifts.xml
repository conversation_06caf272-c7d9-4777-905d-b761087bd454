{extends "@layout.xml"}

{block title}
    {$cat->getName()}
{/block}

{block headingWrap}
    <a style="float: right;" class="aui-button aui-button-primary" href="/admin/gifts/id0?act=edit&cat={$cat->getId()}">
        {_create}
    </a>
    
    <h1>{_admin_giftset} "{$cat->getName()}"</h1>
{/block}

{block content}
    {if sizeof($gifts) > 0}
        <table class="aui aui-table-list">
            <thead>
                <th>Подарок</th>
                <th>Имя</th>
                <th>Цена</th>
                <th>Подарен</th>
                <th>Ограничение</th>
                <th>Сброс счётчика ограничений</th>
                <th>Действия</th>
            </thead>
            <tbody>
                <tr n:foreach="$gifts as $gift">
                    <td style="vertical-align: middle; width: 0px;">
                        <img style="max-width: 32px;" src="{$gift->getImage(2)}" alt="{$gift->getName()}" />
                    </td>
                    <td style="vertical-align: middle;">
                        {$gift->getName()}
                        <span n:if="$gift->isFree()" class="aui-lozenge aui-lozenge-subtle aui-lozenge-success">
                            {_admin_price_free}
                        </span>
                    </td>
                    <td style="vertical-align: middle;">
                        {tr("points_amount", $gift->getPrice())}
                    </td>
                    <td style="vertical-align: middle;">
                        {$gift->getUsages()} раз
                    </td>
                    <td style="vertical-align: middle;">
                        {if $gift->getLimit() === INF}
                            Отсутствует
                        {else}
                            Не более {$gift->getLimit()} дарений
                        {/if}
                    </td>
                    <td style="vertical-align: middle;">
                        {if !$gift->getLimitResetTime()}
                            Никогда
                        {else}
                            Последний раз в
                            {$gift->getLimitResetTime()->format("%a, %d %B %G")}
                        {/if}
                    </td>
                    <td style="vertical-align: middle; text-align: right;">
                        <a class="aui-button aui-button-primary" href="/admin/gifts/id{$gift->getId()}">
                            <span class="aui-icon aui-icon-small aui-iconfont-new-edit">Редактировать</span>
                        </a>
                    </td>
                </tr>
            </tbody>
        </table>
    {else}
        <center>
            <p>Подарков нету. Нажмите на красивую кнопку вверху, чтобы создать первый.</p>
        </center>
    {/if}

    <div align="right">
        {var $isLast = ((20 * (($_GET['p'] ?? 1) - 1)) + sizeof($gifts)) < $count}

        <a n:if="($_GET['p'] ?? 1) > 1" class="aui-button" href="?p={($_GET['p'] ?? 1) - 1}">&laquo;</a>
        <a n:if="$isLast" class="aui-button" href="?p={($_GET['p'] ?? 1) + 1}">&raquo;</a>
    </div>
{/block}
