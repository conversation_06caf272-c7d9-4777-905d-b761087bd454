{extends "../@layout.xml"}
{block title}{_change_video}{/block}

{block header}
    <a href="{$thisUser->getURL()}">{$thisUser->getCanonicalName()}</a>
    »
    <a href="/videos{$thisUser->getId()}">{_videos}</a>
    »
    <a href="/video{$video->getPrettyId()}">{_video}</a>
    »
    {_change_video}
{/block}

{block content}
<div class="container_gray">
    <h4>{_change_video}</h4>
    <form method="post" enctype="multipart/form-data">
      <table cellspacing="7" cellpadding="0" width="60%" border="0" align="center">
        <tbody>
          <tr>
            <td width="120" valign="top"><span class="nobold">{_name}:</span></td>
            <td>
                <input type="text" value="{$video->getName()}" name="name" />
            </td>
          </tr>
          <tr>
            <td width="120" valign="top"><span class="nobold">{_description}:</span></td>
            <td>
                <textarea name="desc">{$video->getDescription()}</textarea>
            </td>
          </tr>
          <tr>
            <td width="120" valign="top"></td>
            <td>
                <input type="hidden" name="hash" value="{$csrfToken}" />
                <input type="submit" class="button" name="submit" value="{_save}" />
            </td>
          </tr>
        </tbody>
      </table>
    </form>
</div>
{/block}
