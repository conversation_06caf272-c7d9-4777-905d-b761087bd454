{extends "../@layout.xml"}

{block title}{$note->getName()}{/block}

{block header}
    {var $author = $note->getOwner()}
    <a href="{$author->getURL()}">{$author->getCanonicalName()}</a>
    »
    <a href="/notes{$author->getId()}">{_notes}</a>
    »
    {$note->getName()}
{/block}

{block content}
    {var $author = $note->getOwner()}
    <style>
        #userContent img {
            max-width: 245pt;
            max-height: 200pt;
        }

        #userContent blockquote {
            background-color: #f3f3f3;
            border-bottom: 5px solid #969696;
            padding: 1;
        }

        #userContent cite {
            margin-top: 1em;
            display: block;
        }

        #userContent cite::before {
            content: "— ";
        }

        #userContent .underline {
            text-decoration: underline;
        }
    </style>

    <article id="userContent" style="margin: 10px 10px 0;">
        <div class="note_header">
            <div class="note_title">
                <div class="note_title">
                    <a>{$note->getName()}</a>
                </div>
            </div>
            <div class="byline">
                <span><a href="{$author->getURL()}">{$author->getCanonicalName()}</a></span> {$note->getPublicationTime()}
                <span n:if="$note->getEditTime() > $note->getPublicationTime()">({_edited} {$note->getEditTime()})</span>
            </div>
        </div>
        <div style="margin-left: 6px; width: 535px;">
            {$note->getText()|noescape}
        </div>
    </article>

    <div class="note_footer" style="margin: 10px 10px 0;">
        <div class="comments_count">
            {if sizeof($comments) > 0}
                {_comments} ({$note->getCommentsCount()})
            {else}
                {_no_comments}
            {/if}
            <span n:if="isset($thisUser) && $thisUser->getId() === $note->getOwner()->getId()">&nbsp;|&nbsp;
                <a id="_noteDelete" href="/note{$note->getOwner()->getId()}_{$note->getId()}/delete">{_delete}</a>
                &nbsp;|&nbsp;
                <a href="/note{$note->getOwner()->getId()}_{$note->getVirtualId()}/edit" rel='nofollow'>{_edit}</a>
            </span>
        </div>
    </div>
    <div style="margin: 6px 10px 10px;border-top: 1px solid #ddd;">
        {include "../components/comments.xml",
            comments => $comments,
            count => $cCount,
            page => $cPage,
            model => "notes",
            parent => $note,
            showTitle => false}
    </div>
{/block}
