name: Build base images

on:
  schedule:
    - cron: "0 0 * * *"
  workflow_dispatch:

env:
  BASE_IMAGE_NAME: php
  BASE_IMAGE_VERSION: "8.2"

jobs:
  build-cli:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v3
        with:
          lfs: false

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v2

      - name: Set up Docker Buildx
        id: buildx
        uses: docker/setup-buildx-action@v2

      - name: Change repository string to lowercase
        id: repositorystring
        uses: Entepotenz/change-string-case-action-min-dependencies@v1.1.0
        with:
          string: ${{ github.repository }}

      - name: Log into registry
        run: echo "${{ secrets.GITHUB_TOKEN }}" | docker login ghcr.io -u ${{ github.actor }} --password-stdin

      - name: Build cli image
        run: |
          IMAGE_NAME=ghcr.io/${{ steps.repositorystring.outputs.lowercase }}/$BASE_IMAGE_NAME:$BASE_IMAGE_VERSION-cli

          docker buildx build --platform linux/amd64,linux/arm64 -t $IMAGE_NAME . --push -f install/automated/docker/base-php-cli.Dockerfile --build-arg VERSION=$BASE_IMAGE_VERSION

  build-apache:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v3
        with:
          lfs: false

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v2

      - name: Set up Docker Buildx
        id: buildx
        uses: docker/setup-buildx-action@v2

      - name: Change repository string to lowercase
        id: repositorystring
        uses: Entepotenz/change-string-case-action-min-dependencies@v1.1.0
        with:
          string: ${{ github.repository }}

      - name: Log into registry
        run: echo "${{ secrets.GITHUB_TOKEN }}" | docker login ghcr.io -u ${{ github.actor }} --password-stdin

      - name: Build apache image
        run: |
          IMAGE_NAME=ghcr.io/${{ steps.repositorystring.outputs.lowercase }}/$BASE_IMAGE_NAME:$BASE_IMAGE_VERSION-apache

          docker buildx build --platform linux/amd64,linux/arm64 -t $IMAGE_NAME . --push -f install/automated/docker/base-php-apache.Dockerfile --build-arg VERSION=$BASE_IMAGE_VERSION
