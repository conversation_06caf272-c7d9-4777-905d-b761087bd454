{extends "../@layout.xml"}

{block title}
    {if $create}
        {_create_app}
    {else}
        {_edit_app}
    {/if}
{/block}

{block header}
    {if $create}
        {_new_app}
    {else}
        <a href="/apps?act=dev">{_own_apps_alternate}</a> »
        <a href="/app{$id}">{$name}</a> »
        {_edit}
    {/if}
{/block}

{block content}
    <div class="container_gray">
        <h4>{_main_information}</h4>
        <form method="POST" enctype="multipart/form-data">
            <table cellspacing="7" cellpadding="0" width="60%" border="0" align="center">
                <tbody>
                    <tr>
                        <td width="120" valign="top">
                            <span class="nobold">{_name}: </span>
                        </td>
                        <td>
                            <input type="text" name="name" value="{$name ?? ''}" />
                        </td>
                    </tr>
                    <tr>
                        <td width="120" valign="top">
                            <span class="nobold">{_description}: </span>
                        </td>
                        <td>
                            <input type="text" name="desc" value="{$desc ?? ''}" />
                        </td>
                    </tr>
                    <tr>
                        <td width="120" valign="top">
                            <span class="nobold">{_avatar}: </span>
                        </td>
                        <td>
                            <input type="file" name="ava" accept="image/*" />
                        </td>
                    </tr>
                    <tr>
                        <td width="120" valign="top">
                            <span class="nobold">{_app_news}: </span>
                        </td>
                        <td>
                            <input type="text" name="note" placeholder="{ovk_scheme(true) . $_SERVER['HTTP_HOST']}/note{$thisUser->getId()}_10" value="{$note ?? ''}" />
                        </td>
                    </tr>
                    <tr>
                        <td width="120" valign="top">
                            <span class="nobold">URL: </span>
                        </td>
                        <td>
                            <input type="text" name="url" value="{$url ?? ''}" />
                        </td>
                    </tr>
                    <tr>
                        <td width="120" valign="top">
                            <span class="nobold">{_app_state}: </span>
                        </td>
                        <td>
                            <input type="checkbox" name="enable" n:attr="checked => ($on ?? false)" /> {_app_enabled}
                        </td>
                    </tr>
                    <tr>
                        <td>

                        </td>
                        <td>
                            <input type="hidden" name="hash" value="{$csrfToken}" />
                            <input type="submit" value="{_save}" class="button" />
                        </td>
                    </tr>
                </tbody>
            </table>
        </form>
        <br/>

        <h4>{_additional_information}</h4>
        <div>
            <ul style="color: unset;">
                {if $create}
                    <li>{_app_creation_hint_url}</li>
                    <li>{_app_creation_hint_iframe}</li>
                {else}
                    <li>{tr("app_balance", $coins)|noescape} (<a href="javascript:withdraw({$id})">{_app_withdrawal_q}</a>)</li>
                    <li>{tr("app_users", $users)|noescape}</li>
                {/if}
            </ul>
        </div>
    </div>

    <script>
        window.coins = {$coins}
    </script>
{/block}
