{extends "../@listView.xml"}
{var $iterator = iterator_to_array($topics)}
{var $page     = $paginatorConf->page}

{block title}{_discussions} {$club->getCanonicalName()}{/block}

{block header}
    <a href="{$club->getURL()}">{$club->getCanonicalName()}</a> » {_discussions}
    
    <div n:if="$club->isEveryoneCanCreateTopics() || $club->canBeModifiedBy($thisUser)" style="float: right;">
        <a href="/board{$club->getId()}/create">{_create_topic}</a>
    </div>
{/block}

{block tabs}
    <form style="margin-left: 12px;">
        <input name="query" class="header_search_input" placeholder="{_header_search}" value="{$_GET['query'] ?? ''}" style="width: 86%" />
        <input type="submit" class="button" value="{_search_button}" style="width: 7.5%" />
    </form>
    
    <p style="margin-left: 15px;">
        <b>{tr("results", $count)}</b>
    </p>
{/block}

{block actions}
    
{/block}

{* BEGIN ELEMENTS DESCRIPTION *}

{block link|strip|stripHtml}
    /topic{$x->getPrettyId()}
{/block}

{block preview}

{/block}

{block name}
    {$x->getTitle()}
    <div n:if="$x->isPinned()" class="pinned-mark"></div>
{/block}

{block description}
    <div style="float: left;">
        {tr("messages", $x->getCommentsCount())}
    </div>
    {var $lastComment = $x->getLastComment()}
    <div n:if="$lastComment" class="avatar-list-item" style="float: right;">
        <div class="avatar">
            <a href="{$lastComment->getOwner()->getURL()}">
                <img class="ava" src="{$lastComment->getOwner()->getAvatarUrl()}" />
            </a>
        </div>
        <div class="info">
            <a href="{$lastComment->getOwner()->getURL()}" class="title">{$lastComment->getOwner()->getCanonicalName()}</a>
            <div class="subtitle">{_replied} {$lastComment->getPublicationTime()}</div>
        </div>
    </div>
{/block}
