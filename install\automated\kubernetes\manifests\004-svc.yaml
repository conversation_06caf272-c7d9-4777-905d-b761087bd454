apiVersion: v1
items:
- apiVersion: v1
  kind: Service
  metadata:
    name: mariadb-primary-svc
    namespace: openvk
  spec:
    ports:
    - name: mysql
      port: 3306
    selector:
      app: mariadb-primary
    type: ClusterIP
- apiVersion: v1
  kind: Service
  metadata:
    name: mariadb-eventdb-svc
    namespace: openvk
  spec:
    ports:
    - name: mysql
      port: 3306
    selector:
      app: mariadb-eventdb
    type: ClusterIP
- apiVersion: v1
  kind: Service
  metadata:
    name: openvk-svc
    namespace: openvk
  spec:
    ports:
    - name: http
      port: 80
    selector:
      app: openvk
    type: ClusterIP
- apiVersion: v1
  kind: Service
  metadata:
    name: phpmyadmin-svc
    namespace: openvk
  spec:
    ports:
    - name: http
      port: 80
    selector:
      app: phpmyadmin
    type: ClusterIP
- apiVersion: v1
  kind: Service
  metadata:
    name: adminer-svc
    namespace: openvk
  spec:
    ports:
    - name: http
      port: 8080
    selector:
      app: adminer
    type: ClusterIP
kind: List
metadata: {}