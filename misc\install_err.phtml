<!DOCTYPE html>
<html>
    <head>
        <title>OpenVK | Installation Error</title>
        <meta charset="utf-8" />
        <style>
            body {
                position: relative;
                background-color: #bbb;
                width: 100vw;
                height: 100vh;
                overflow: hidden;
            }

            .ovk-install-error {
                position: absolute;
                width: 640px;
                height: 460px;
                font-family: sans-serif;
                background: #fff;
                border: 2px outset #afafaf;
                top: 50%;
                left: 50%;
                margin-right: -50%;
                transform: translate(-50%, -50%);
            }

            #title {
                background: #2b2b2b;
                color: #fff;
                box-sizing: border-box;
                height: 78px;
            }

            #title h1 {
                margin: 0;
                padding: 20px 10px;
            }

            #main {
                height: calc(100% - 78px);
                width: 100%;
                box-sizing: border-box;
                padding:  8px;
                overflow-x: hidden;
                overflow-y: auto;
            }

            ol {
                list-style: circle;
            }
        </style>
    </head>
    <body>
        <div class="ovk-install-error">
            <div id="title">
                <h1 style="">Installation error</h1>
            </div>
            <div id="main">
                <h3>Detected problems</h3>
                <ol>
                    <?php foreach($problems as $problem): ?>
                        <li><?= $problem ?></li>
                    <?php endforeach; ?>
                </ol>
                <hr>
                <p><b>Notice</b>: if you've just installed OpenVK, don't panic, you probably just need to install some dependencies via composer.</p>
            </div>
        </div>
    </body>
</html>
