openvk:
    debug: true
    appearance:
        name: "FreeBSD Instance"
        motd: "Yet another OpenVK instance"

    preferences:
        femaleGenderPriority: true
        nginxCacheTime: 120
        uploads:
            disableLargeUploads: false
            mode: "basic"
            api:
                maxFilesPerDomain: 10
                maxFileSize: 25000000
        shortcodes:
            minLength: 3 # won't affect existing short urls or the ones set via admin panel
            forbiddenNames:
                - "index.php"
        photos:
            upgradeStructure: false
        security:
            requireEmail: false
            requirePhone: false
            forcePhoneVerification: false
            forceEmailVerification: false
            enableSu: true
            rateLimits:
                actions: 5
                time: 20
                maxViolations: 50
                maxViolationsAge: 120
                autoban: true
        registration:
            enable: true
            reason: "" # reason for disabling registration
        support:
            supportName: "Moderator"
            adminAccount: 1 # Change this ok
            fastAnswers:
                - "This is a list of quick answers to common questions for support. Post your responses here and agents can send it quickly with just 3 clicks"
                - "There can be as many answers as you want, but it is best to have a maximum of 10.\n\nYou can also remove all answers from the list to disable this feature"
                - "Good luck filling! If you are a regular support agent, inform the administrator that he forgot to fill the config"
        messages:
            strict: false
        wall:
            christian: false
            anonymousPosting:
                enable: false
                account: 100
            postSizes:
                maxSize: 60000
                processingLimit: 3000
                emojiProcessingLimit: 1000
        commerce: false
        menu:
            links:
                - name: "@left_menu_donate"
                  url: "/donate"
        adPoster:
            enable: false
            src: "https://example.org/ad_poster.jpeg"
            caption: "Ad caption"
            link: "https://example.org/product.aspx?id=10&from=ovk"
        bellsAndWhistles:
            testLabel: false
        defaultMobileTheme: ""

    telemetry:
        plausible:
            enable: false
            domain: ""
            server: ""
        piwik:
            enable: false
            container: ""
            site: ""
            layer: "dataLayer"
        matomo:
            enable: false
            container: ""
            site: ""

    credentials:
        smsc:
            enable: false
            client: ""
            secret: "SECRET_KEY_HERE"
        telegram:
            enable: false
            token: "TOKEN_HERE"
            helpdeskChat: ""
        eventDB:
            enable: true # Better enable this
            database:
                dsn: "mysql:unix_socket=/var/run/mysql/mysql.sock;dbname=openvk-eventdb"
                user: "root"
                password: "justMonika"
        notificationsBroker:
            enable: false
            kafka:
                addr: "127.0.0.1"
                port: 9092
                topic: "OvkEvents"
