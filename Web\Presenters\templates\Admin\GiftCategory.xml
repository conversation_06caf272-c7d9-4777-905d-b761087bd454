{extends "@layout.xml"}

{block title}
    {if $form->id === 0}
        {_admin_giftsets_create}
    {else}
        {$form->languages["master"]->name}
    {/if}
{/block}

{block heading}
    {include title}
{/block}

{block content}
    <form class="aui" method="POST">
        <h3>{_admin_commonsettings}</h3>
        <fieldset>
            <div class="field-group">
                <label for="id">
                    ID
                </label>
                <input class="text long-field" type="number" id="id" name="id" disabled value="{$form->id}" />
            </div>
            <div class="field-group">
                <label for="name_master">
                    {_admin_name}
                    <span class="aui-icon icon-required"></span>
                </label>
                <input class="text long-field" type="text" id="name_master" name="name_master" value="{$form->languages['master']->name}" />
                <div class="description">{_admin_giftsets_title}</div>
            </div>
            <div class="field-group">
                <label for="description_master">
                    {_admin_description}
                    <span class="aui-icon icon-required"></span>
                </label>
                <input class="text long-field" type="text" id="description_master" name="description_master" value="{$form->languages['master']->description}" />
                <div class="description">{_admin_giftsets_description}</div>
            </div>
        </fieldset>
        
        <h3>{_admin_langsettings}</h3>
        <fieldset>
            {foreach $form->languages as $locale => $data}
                {continueIf $locale === "master"}
                
                <div class="field-group">
                    <label for="name_{$locale}">
                        {_admin_name}
                        <img src="/assets/packages/static/openvk/img/flags/{$locale}.gif" alt="{$locale}" />
                    </label>
                    <input class="text long-field" type="text" id="name_{$locale}" name="name_{$locale}" value="{$data->name}" />
                </div>
                <div class="field-group">
                    <label for="description_{$locale}">
                        {_admin_description}
                        <img src="/assets/packages/static/openvk/img/flags/{$locale}.gif" alt="{$locale}" />
                    </label>
                    <input class="text long-field" type="text" id="description_{$locale}" name="description_{$locale}" value="{$data->description}" />
                </div>
            {/foreach}
        </fieldset>
        
        <div class="buttons-container">
            <div class="buttons">
                <input type="hidden" name="hash" value="{$csrfToken}" />
                <input class="aui-button aui-button-primary submit" type="submit" value="{_save}">
            </div>
        </div>
    </form>
{/block}
