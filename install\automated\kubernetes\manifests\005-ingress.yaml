apiVersion: v1
items:
- apiVersion: networking.k8s.io/v1
  kind: Ingress
  metadata:
    annotations: {}
    name: openvk
    namespace: openvk
  spec:
    rules:
    - host: openvk.local
      http:
        paths:
        - backend:
            service:
              name: openvk-svc
              port:
                number: 80
          path: /
          pathType: Prefix
- apiVersion: networking.k8s.io/v1
  kind: Ingress
  metadata:
    annotations: {}
    name: phpmyadmin
    namespace: openvk
  spec:
    rules:
    - host: phpmyadmin.local
      http:
        paths:
        - backend:
            service:
              name: phpmyadmin-svc
              port:
                number: 80
          path: /
          pathType: Prefix
- apiVersion: networking.k8s.io/v1
  kind: Ingress
  metadata:
    annotations: {}
    name: adminer
    namespace: openvk
  spec:
    rules:
    - host: adminer.local
      http:
        paths:
        - backend:
            service:
              name: adminer-svc
              port:
                number: 8080
          path: /
          pathType: Prefix
kind: List
metadata: {}